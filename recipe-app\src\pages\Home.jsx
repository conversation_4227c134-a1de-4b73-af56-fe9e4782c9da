import React, { useEffect, useState } from 'react';
import { useRecipes } from '../context/RecipeContext';
import SearchBar from '../components/SearchBar';
import RecipeCard from '../components/RecipeCard';
import { ChefHat, Plus, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';

// Sample recipe data
const sampleRecipes = [
  {
    id: '1',
    title: 'Classic Pancakes',
    description: 'Fluffy and delicious pancakes perfect for breakfast. Made with simple ingredients you probably already have in your kitchen.',
    category: 'breakfast',
    cookTime: 20,
    servings: 4,
    difficulty: 'easy',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop',
    ingredients: [
      '2 cups all-purpose flour',
      '2 tablespoons sugar',
      '2 teaspoons baking powder',
      '1 teaspoon salt',
      '2 large eggs',
      '1 3/4 cups milk',
      '1/4 cup melted butter'
    ],
    instructions: [
      'In a large bowl, whisk together flour, sugar, baking powder, and salt.',
      'In another bowl, beat eggs and then whisk in milk and melted butter.',
      'Pour the wet ingredients into the dry ingredients and stir until just combined.',
      'Heat a griddle or large skillet over medium heat.',
      'Pour 1/4 cup of batter for each pancake onto the griddle.',
      'Cook until bubbles form on surface, then flip and cook until golden brown.'
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Spaghetti Carbonara',
    description: 'Authentic Italian pasta dish with eggs, cheese, and pancetta. Creamy and rich without using cream.',
    category: 'dinner',
    cookTime: 25,
    servings: 4,
    difficulty: 'medium',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop',
    ingredients: [
      '400g spaghetti',
      '200g pancetta or guanciale',
      '4 large eggs',
      '100g Pecorino Romano cheese',
      '50g Parmesan cheese',
      'Black pepper',
      'Salt'
    ],
    instructions: [
      'Bring a large pot of salted water to boil and cook spaghetti according to package directions.',
      'While pasta cooks, cut pancetta into small cubes and cook in a large skillet until crispy.',
      'In a bowl, whisk together eggs, grated cheeses, and plenty of black pepper.',
      'Reserve 1 cup of pasta water before draining.',
      'Add hot pasta to the skillet with pancetta.',
      'Remove from heat and quickly stir in egg mixture, adding pasta water as needed to create a creamy sauce.'
    ],
    createdAt: '2024-01-16T14:30:00Z',
    updatedAt: '2024-01-16T14:30:00Z'
  },
  {
    id: '3',
    title: 'Chocolate Chip Cookies',
    description: 'Soft and chewy chocolate chip cookies that are perfect for any occasion. A classic recipe that never fails.',
    category: 'dessert',
    cookTime: 15,
    servings: 24,
    difficulty: 'easy',
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=300&fit=crop',
    ingredients: [
      '2 1/4 cups all-purpose flour',
      '1 teaspoon baking soda',
      '1 teaspoon salt',
      '1 cup butter, softened',
      '3/4 cup granulated sugar',
      '3/4 cup brown sugar',
      '2 large eggs',
      '2 teaspoons vanilla extract',
      '2 cups chocolate chips'
    ],
    instructions: [
      'Preheat oven to 375°F (190°C).',
      'In a bowl, combine flour, baking soda, and salt.',
      'In a large bowl, beat butter and both sugars until creamy.',
      'Beat in eggs and vanilla extract.',
      'Gradually blend in flour mixture.',
      'Stir in chocolate chips.',
      'Drop rounded tablespoons of dough onto ungreased cookie sheets.',
      'Bake for 9-11 minutes or until golden brown.'
    ],
    createdAt: '2024-01-17T16:45:00Z',
    updatedAt: '2024-01-17T16:45:00Z'
  }
];

const Home = () => {
  const { recipes, filteredRecipes, setRecipes, loading } = useRecipes();
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Load sample data on component mount
  useEffect(() => {
    if (recipes.length === 0) {
      // Simulate loading for better UX
      setTimeout(() => {
        setRecipes(sampleRecipes);
        setIsInitialLoading(false);
      }, 1000);
    } else {
      setIsInitialLoading(false);
    }
  }, [recipes.length, setRecipes]);

  if (loading || isInitialLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-center animate-fadeIn">
          <ChefHat className="h-16 w-16 text-white mx-auto mb-4 animate-bounce neon-white" />
          <div className="flex items-center justify-center space-x-1 mb-2">
            <Sparkles className="h-5 w-5 text-white animate-pulse" />
            <p className="text-xl font-medium text-white text-glow">Loading delicious recipes...</p>
            <Sparkles className="h-5 w-5 text-white animate-pulse" />
          </div>
          <div className="w-32 h-1 bg-gray-800 rounded-full mx-auto overflow-hidden">
            <div className="h-full bg-white rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8 animate-fadeIn">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="h-8 w-8 text-white animate-pulse" />
            <h1 className="text-4xl md:text-5xl font-bold text-white text-glow">
              Discover Amazing Recipes
            </h1>
            <Sparkles className="h-8 w-8 text-white animate-pulse" />
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto animate-slideIn">
            Explore our collection of delicious recipes from around the world.
            Find your next favorite dish or share your own culinary creations.
          </p>
        </div>

        {/* Search Bar */}
        <SearchBar />

        {/* Quick Actions */}
        <div className="mb-8 animate-slideIn">
          <Link
            to="/add-recipe"
            className="inline-flex items-center space-x-2 bg-white text-black px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-300 shadow-md hover-lift group neon-white"
          >
            <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
            <span className="font-medium">Add New Recipe</span>
          </Link>
        </div>

        {/* Recipe Grid */}
        {filteredRecipes.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRecipes.map((recipe, index) => (
              <div
                key={recipe.id}
                className="animate-fadeIn"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <RecipeCard recipe={recipe} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 animate-fadeIn">
            <ChefHat className="h-16 w-16 text-gray-400 mx-auto mb-4 animate-bounce" />
            <h3 className="text-lg font-medium text-white mb-2">No recipes found</h3>
            <p className="text-gray-300 mb-6">
              {recipes.length === 0
                ? "Get started by adding your first recipe!"
                : "Try adjusting your search criteria or add a new recipe."
              }
            </p>
            <Link
              to="/add-recipe"
              className="inline-flex items-center space-x-2 bg-white text-black px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 hover-lift group neon-white"
            >
              <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
              <span>Add Recipe</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Home;
