import React, { useEffect, useState } from 'react';
import { useRecipes } from '../context/RecipeContext';
import { useTheme } from '../context/ThemeContext';
import SearchBar from '../components/SearchBar';
import RecipeCard from '../components/RecipeCard';
import { ChefHat, Plus, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';

// Sample recipe data
const sampleRecipes = [
  {
    id: '1',
    title: 'Classic Pancakes',
    description: 'Fluffy and delicious pancakes perfect for breakfast. Made with simple ingredients you probably already have in your kitchen.',
    category: 'breakfast',
    cookTime: 20,
    servings: 4,
    difficulty: 'easy',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop',
    ingredients: [
      '2 cups all-purpose flour',
      '2 tablespoons sugar',
      '2 teaspoons baking powder',
      '1 teaspoon salt',
      '2 large eggs',
      '1 3/4 cups milk',
      '1/4 cup melted butter'
    ],
    instructions: [
      'In a large bowl, whisk together flour, sugar, baking powder, and salt.',
      'In another bowl, beat eggs and then whisk in milk and melted butter.',
      'Pour the wet ingredients into the dry ingredients and stir until just combined.',
      'Heat a griddle or large skillet over medium heat.',
      'Pour 1/4 cup of batter for each pancake onto the griddle.',
      'Cook until bubbles form on surface, then flip and cook until golden brown.'
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Spaghetti Carbonara',
    description: 'Authentic Italian pasta dish with eggs, cheese, and pancetta. Creamy and rich without using cream.',
    category: 'dinner',
    cookTime: 25,
    servings: 4,
    difficulty: 'medium',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop',
    ingredients: [
      '400g spaghetti',
      '200g pancetta or guanciale',
      '4 large eggs',
      '100g Pecorino Romano cheese',
      '50g Parmesan cheese',
      'Black pepper',
      'Salt'
    ],
    instructions: [
      'Bring a large pot of salted water to boil and cook spaghetti according to package directions.',
      'While pasta cooks, cut pancetta into small cubes and cook in a large skillet until crispy.',
      'In a bowl, whisk together eggs, grated cheeses, and plenty of black pepper.',
      'Reserve 1 cup of pasta water before draining.',
      'Add hot pasta to the skillet with pancetta.',
      'Remove from heat and quickly stir in egg mixture, adding pasta water as needed to create a creamy sauce.'
    ],
    createdAt: '2024-01-16T14:30:00Z',
    updatedAt: '2024-01-16T14:30:00Z'
  },
  {
    id: '3',
    title: 'Chocolate Chip Cookies',
    description: 'Soft and chewy chocolate chip cookies that are perfect for any occasion. A classic recipe that never fails.',
    category: 'dessert',
    cookTime: 15,
    servings: 24,
    difficulty: 'easy',
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=300&fit=crop',
    ingredients: [
      '2 1/4 cups all-purpose flour',
      '1 teaspoon baking soda',
      '1 teaspoon salt',
      '1 cup butter, softened',
      '3/4 cup granulated sugar',
      '3/4 cup brown sugar',
      '2 large eggs',
      '2 teaspoons vanilla extract',
      '2 cups chocolate chips'
    ],
    instructions: [
      'Preheat oven to 375°F (190°C).',
      'In a bowl, combine flour, baking soda, and salt.',
      'In a large bowl, beat butter and both sugars until creamy.',
      'Beat in eggs and vanilla extract.',
      'Gradually blend in flour mixture.',
      'Stir in chocolate chips.',
      'Drop rounded tablespoons of dough onto ungreased cookie sheets.',
      'Bake for 9-11 minutes or until golden brown.'
    ],
    createdAt: '2024-01-17T16:45:00Z',
    updatedAt: '2024-01-17T16:45:00Z'
  },
  {
    id: '4',
    title: 'Avocado Toast',
    description: 'Healthy and delicious avocado toast topped with fresh herbs and a perfectly poached egg.',
    category: 'breakfast',
    cookTime: 10,
    servings: 2,
    difficulty: 'easy',
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=300&fit=crop',
    ingredients: [
      '2 slices whole grain bread',
      '1 ripe avocado',
      '2 eggs',
      '1 tablespoon lemon juice',
      'Salt and pepper to taste',
      'Red pepper flakes',
      'Fresh herbs (chives or cilantro)',
      'Extra virgin olive oil'
    ],
    instructions: [
      'Toast the bread slices until golden brown.',
      'Mash the avocado with lemon juice, salt, and pepper.',
      'Poach the eggs in simmering water for 3-4 minutes.',
      'Spread avocado mixture on toast.',
      'Top with poached egg.',
      'Sprinkle with red pepper flakes and fresh herbs.',
      'Drizzle with olive oil and serve immediately.'
    ],
    createdAt: '2024-01-18T08:30:00Z',
    updatedAt: '2024-01-18T08:30:00Z'
  },
  {
    id: '5',
    title: 'Thai Green Curry',
    description: 'Aromatic and spicy Thai green curry with coconut milk, vegetables, and your choice of protein.',
    category: 'dinner',
    cookTime: 35,
    servings: 4,
    difficulty: 'medium',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=400&h=300&fit=crop',
    ingredients: [
      '2 tablespoons green curry paste',
      '400ml coconut milk',
      '500g chicken breast, sliced',
      '1 eggplant, cubed',
      '1 bell pepper, sliced',
      '100g green beans',
      '2 tablespoons fish sauce',
      '1 tablespoon brown sugar',
      'Thai basil leaves',
      'Jasmine rice for serving'
    ],
    instructions: [
      'Heat 2 tablespoons of coconut milk in a wok over medium heat.',
      'Add green curry paste and fry for 2 minutes until fragrant.',
      'Add chicken and cook until no longer pink.',
      'Pour in remaining coconut milk and bring to a simmer.',
      'Add vegetables and cook for 10-15 minutes.',
      'Season with fish sauce and brown sugar.',
      'Stir in Thai basil leaves.',
      'Serve hot with jasmine rice.'
    ],
    createdAt: '2024-01-19T19:15:00Z',
    updatedAt: '2024-01-19T19:15:00Z'
  },
  {
    id: '6',
    title: 'Caesar Salad',
    description: 'Classic Caesar salad with crispy romaine lettuce, parmesan cheese, and homemade croutons.',
    category: 'lunch',
    cookTime: 20,
    servings: 4,
    difficulty: 'easy',
    rating: 4.5,
    image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=300&fit=crop',
    ingredients: [
      '2 heads romaine lettuce',
      '1/2 cup parmesan cheese, grated',
      '2 cups bread cubes',
      '3 cloves garlic',
      '2 anchovy fillets',
      '1 egg yolk',
      '2 tablespoons lemon juice',
      '1/4 cup olive oil',
      'Salt and black pepper'
    ],
    instructions: [
      'Make croutons by tossing bread cubes with olive oil and baking at 375°F for 10 minutes.',
      'Wash and chop romaine lettuce.',
      'Make dressing by whisking together garlic, anchovies, egg yolk, and lemon juice.',
      'Slowly add olive oil while whisking.',
      'Toss lettuce with dressing.',
      'Top with parmesan cheese and croutons.',
      'Season with salt and pepper to taste.'
    ],
    createdAt: '2024-01-20T12:30:00Z',
    updatedAt: '2024-01-20T12:30:00Z'
  },
  {
    id: '7',
    title: 'Beef Tacos',
    description: 'Authentic Mexican beef tacos with seasoned ground beef, fresh toppings, and warm tortillas.',
    category: 'dinner',
    cookTime: 25,
    servings: 6,
    difficulty: 'easy',
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
    ingredients: [
      '1 lb ground beef',
      '8 corn tortillas',
      '1 onion, diced',
      '2 cloves garlic, minced',
      '1 tablespoon chili powder',
      '1 teaspoon cumin',
      '1/2 teaspoon paprika',
      'Lettuce, shredded',
      'Tomatoes, diced',
      'Cheese, shredded',
      'Sour cream',
      'Lime wedges'
    ],
    instructions: [
      'Cook ground beef in a large skillet over medium heat.',
      'Add onion and garlic, cook until softened.',
      'Add spices and cook for 1 minute.',
      'Warm tortillas in a dry skillet or microwave.',
      'Fill tortillas with beef mixture.',
      'Top with lettuce, tomatoes, cheese, and sour cream.',
      'Serve with lime wedges.'
    ],
    createdAt: '2024-01-21T18:00:00Z',
    updatedAt: '2024-01-21T18:00:00Z'
  },
  {
    id: '8',
    title: 'Blueberry Muffins',
    description: 'Fluffy and moist blueberry muffins bursting with fresh berries and a golden top.',
    category: 'breakfast',
    cookTime: 25,
    servings: 12,
    difficulty: 'easy',
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=300&fit=crop',
    ingredients: [
      '2 cups all-purpose flour',
      '3/4 cup sugar',
      '2 teaspoons baking powder',
      '1/2 teaspoon salt',
      '1/3 cup melted butter',
      '1 egg',
      '1 cup milk',
      '1 cup fresh blueberries',
      '1 teaspoon vanilla extract'
    ],
    instructions: [
      'Preheat oven to 400°F (200°C).',
      'Mix dry ingredients in a large bowl.',
      'Combine wet ingredients in another bowl.',
      'Fold wet ingredients into dry ingredients until just combined.',
      'Gently fold in blueberries.',
      'Fill muffin cups 2/3 full.',
      'Bake for 20-25 minutes until golden brown.',
      'Cool in pan for 5 minutes before removing.'
    ],
    createdAt: '2024-01-22T07:45:00Z',
    updatedAt: '2024-01-22T07:45:00Z'
  },
  {
    id: '9',
    title: 'Caprese Salad',
    description: 'Fresh and simple Italian salad with tomatoes, mozzarella, basil, and balsamic glaze.',
    category: 'appetizer',
    cookTime: 10,
    servings: 4,
    difficulty: 'easy',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1608897013039-887f21d8c804?w=400&h=300&fit=crop',
    ingredients: [
      '4 large tomatoes, sliced',
      '8 oz fresh mozzarella, sliced',
      'Fresh basil leaves',
      '3 tablespoons extra virgin olive oil',
      '2 tablespoons balsamic glaze',
      'Salt and pepper to taste',
      'Flaky sea salt for finishing'
    ],
    instructions: [
      'Arrange tomato and mozzarella slices alternately on a platter.',
      'Tuck basil leaves between the slices.',
      'Drizzle with olive oil and balsamic glaze.',
      'Season with salt and pepper.',
      'Finish with a sprinkle of flaky sea salt.',
      'Serve immediately at room temperature.'
    ],
    createdAt: '2024-01-23T11:20:00Z',
    updatedAt: '2024-01-23T11:20:00Z'
  },
  {
    id: '10',
    title: 'Chicken Stir Fry',
    description: 'Quick and healthy chicken stir fry with colorful vegetables in a savory sauce.',
    category: 'dinner',
    cookTime: 20,
    servings: 4,
    difficulty: 'easy',
    rating: 4.5,
    image: 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop',
    ingredients: [
      '1 lb chicken breast, sliced thin',
      '2 tablespoons vegetable oil',
      '1 bell pepper, sliced',
      '1 broccoli head, cut into florets',
      '1 carrot, sliced',
      '2 cloves garlic, minced',
      '1 tablespoon ginger, minced',
      '3 tablespoons soy sauce',
      '1 tablespoon oyster sauce',
      '1 teaspoon cornstarch',
      'Green onions for garnish',
      'Cooked rice for serving'
    ],
    instructions: [
      'Heat oil in a large wok or skillet over high heat.',
      'Add chicken and cook until no longer pink.',
      'Add vegetables and stir-fry for 3-4 minutes.',
      'Add garlic and ginger, cook for 30 seconds.',
      'Mix soy sauce, oyster sauce, and cornstarch.',
      'Pour sauce over chicken and vegetables.',
      'Stir-fry for 1-2 minutes until sauce thickens.',
      'Garnish with green onions and serve over rice.'
    ],
    createdAt: '2024-01-24T17:30:00Z',
    updatedAt: '2024-01-24T17:30:00Z'
  },
  {
    id: '11',
    title: 'Margherita Pizza',
    description: 'Classic Italian pizza with fresh mozzarella, tomatoes, and basil on a crispy thin crust.',
    category: 'dinner',
    cookTime: 45,
    servings: 4,
    difficulty: 'medium',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400&h=300&fit=crop',
    ingredients: [
      '1 pizza dough',
      '1/2 cup pizza sauce',
      '8 oz fresh mozzarella, sliced',
      '2 large tomatoes, sliced',
      'Fresh basil leaves',
      '2 tablespoons olive oil',
      'Salt and pepper to taste',
      'Parmesan cheese for garnish'
    ],
    instructions: [
      'Preheat oven to 475°F (245°C).',
      'Roll out pizza dough on a floured surface.',
      'Transfer to a pizza stone or baking sheet.',
      'Spread pizza sauce evenly over dough.',
      'Add mozzarella slices and tomato slices.',
      'Drizzle with olive oil and season with salt and pepper.',
      'Bake for 12-15 minutes until crust is golden.',
      'Top with fresh basil and serve immediately.'
    ],
    createdAt: '2024-01-25T19:00:00Z',
    updatedAt: '2024-01-25T19:00:00Z'
  },
  {
    id: '12',
    title: 'Chicken Tikka Masala',
    description: 'Creamy and flavorful Indian curry with tender chicken in a rich tomato-based sauce.',
    category: 'dinner',
    cookTime: 40,
    servings: 6,
    difficulty: 'medium',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400&h=300&fit=crop',
    ingredients: [
      '2 lbs chicken breast, cubed',
      '1 cup plain yogurt',
      '2 tablespoons garam masala',
      '1 onion, diced',
      '4 cloves garlic, minced',
      '1 inch ginger, minced',
      '1 can crushed tomatoes',
      '1 cup heavy cream',
      '2 tablespoons butter',
      'Cilantro for garnish',
      'Basmati rice for serving'
    ],
    instructions: [
      'Marinate chicken in yogurt and half the garam masala for 30 minutes.',
      'Cook chicken in a large skillet until browned.',
      'In the same pan, sauté onion, garlic, and ginger.',
      'Add remaining garam masala and cook for 1 minute.',
      'Add crushed tomatoes and simmer for 10 minutes.',
      'Stir in cream and butter.',
      'Return chicken to the sauce and simmer for 10 minutes.',
      'Garnish with cilantro and serve over basmati rice.'
    ],
    createdAt: '2024-01-26T18:30:00Z',
    updatedAt: '2024-01-26T18:30:00Z'
  },
  {
    id: '13',
    title: 'French Toast',
    description: 'Golden, fluffy French toast with a crispy exterior and custardy center, perfect for weekend brunch.',
    category: 'breakfast',
    cookTime: 15,
    servings: 4,
    difficulty: 'easy',
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400&h=300&fit=crop',
    ingredients: [
      '8 slices thick bread',
      '4 large eggs',
      '1 cup milk',
      '2 tablespoons sugar',
      '1 teaspoon vanilla extract',
      '1/2 teaspoon cinnamon',
      'Pinch of salt',
      'Butter for cooking',
      'Maple syrup for serving',
      'Powdered sugar for dusting'
    ],
    instructions: [
      'Whisk together eggs, milk, sugar, vanilla, cinnamon, and salt.',
      'Heat butter in a large skillet over medium heat.',
      'Dip each bread slice in the egg mixture.',
      'Cook for 2-3 minutes per side until golden brown.',
      'Serve hot with maple syrup and powdered sugar.',
      'Optional: top with fresh berries or whipped cream.'
    ],
    createdAt: '2024-01-27T09:00:00Z',
    updatedAt: '2024-01-27T09:00:00Z'
  },
  {
    id: '14',
    title: 'Greek Salad',
    description: 'Fresh Mediterranean salad with crisp vegetables, olives, and feta cheese in a lemon vinaigrette.',
    category: 'lunch',
    cookTime: 15,
    servings: 4,
    difficulty: 'easy',
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=300&fit=crop',
    ingredients: [
      '4 large tomatoes, chopped',
      '1 cucumber, sliced',
      '1 red onion, thinly sliced',
      '1 bell pepper, chopped',
      '1/2 cup Kalamata olives',
      '6 oz feta cheese, cubed',
      '1/4 cup olive oil',
      '2 tablespoons lemon juice',
      '1 teaspoon oregano',
      'Salt and pepper to taste'
    ],
    instructions: [
      'Combine tomatoes, cucumber, onion, and bell pepper in a large bowl.',
      'Add olives and feta cheese.',
      'Whisk together olive oil, lemon juice, oregano, salt, and pepper.',
      'Pour dressing over salad and toss gently.',
      'Let sit for 10 minutes to allow flavors to meld.',
      'Serve chilled or at room temperature.'
    ],
    createdAt: '2024-01-28T12:15:00Z',
    updatedAt: '2024-01-28T12:15:00Z'
  },
  {
    id: '15',
    title: 'Chocolate Lava Cake',
    description: 'Decadent individual chocolate cakes with a molten chocolate center that flows when cut.',
    category: 'dessert',
    cookTime: 20,
    servings: 4,
    difficulty: 'medium',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop',
    ingredients: [
      '4 oz dark chocolate, chopped',
      '4 tablespoons butter',
      '2 large eggs',
      '2 tablespoons sugar',
      '2 tablespoons all-purpose flour',
      'Pinch of salt',
      'Butter for ramekins',
      'Cocoa powder for dusting',
      'Vanilla ice cream for serving'
    ],
    instructions: [
      'Preheat oven to 425°F (220°C).',
      'Butter four ramekins and dust with cocoa powder.',
      'Melt chocolate and butter in a double boiler.',
      'Whisk eggs and sugar until thick and pale.',
      'Fold in melted chocolate mixture.',
      'Gently fold in flour and salt.',
      'Divide batter among ramekins.',
      'Bake for 12-14 minutes until edges are firm.',
      'Let cool for 1 minute, then invert onto plates.',
      'Serve immediately with vanilla ice cream.'
    ],
    createdAt: '2024-01-29T20:45:00Z',
    updatedAt: '2024-01-29T20:45:00Z'
  }
];

const Home = () => {
  const { recipes, filteredRecipes, setRecipes, loading } = useRecipes();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { colors } = useTheme();

  // Load sample data on component mount
  useEffect(() => {
    if (recipes.length === 0) {
      // Simulate loading for better UX
      setTimeout(() => {
        setRecipes(sampleRecipes);
        setIsInitialLoading(false);
      }, 1000);
    } else {
      setIsInitialLoading(false);
    }
  }, [recipes.length, setRecipes]);

  if (loading || isInitialLoading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br ${colors.bg.primary} flex items-center justify-center`}>
        <div className="text-center animate-fadeIn">
          <ChefHat className={`h-16 w-16 ${colors.text.primary} mx-auto mb-4 animate-bounce`} />
          <div className="flex items-center justify-center space-x-1 mb-2">
            <Sparkles className={`h-5 w-5 ${colors.text.primary} animate-pulse`} />
            <p className={`text-xl font-medium ${colors.text.primary}`}>Loading delicious recipes...</p>
            <Sparkles className={`h-5 w-5 ${colors.text.primary} animate-pulse`} />
          </div>
          <div className={`w-32 h-1 ${colors.bg.secondary} rounded-full mx-auto overflow-hidden`}>
            <div className={`h-full ${colors.text.primary} rounded-full animate-pulse`}></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br ${colors.bg.primary}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8 animate-fadeIn">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className={`h-8 w-8 ${colors.text.accent} animate-pulse`} />
            <h1 className={`text-4xl md:text-5xl font-bold ${colors.text.primary} bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
              Discover Amazing Recipes
            </h1>
            <Sparkles className={`h-8 w-8 ${colors.text.accent} animate-pulse`} />
          </div>
          <p className={`text-xl ${colors.text.secondary} max-w-2xl mx-auto animate-slideIn`}>
            Explore our collection of delicious recipes from around the world.
            Find your next favorite dish or share your own culinary creations.
          </p>
        </div>

        {/* Search Bar */}
        <SearchBar />

        {/* Quick Actions */}
        <div className="mb-8 animate-slideIn">
          <Link
            to="/add-recipe"
            className={`inline-flex items-center space-x-2 ${colors.button.primary} px-8 py-4 rounded-xl transition-all duration-300 shadow-md hover-lift group`}
          >
            <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
            <span className="font-medium">Add New Recipe</span>
          </Link>
        </div>

        {/* Recipe Grid */}
        {filteredRecipes.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRecipes.map((recipe, index) => (
              <div
                key={recipe.id}
                className="animate-fadeIn"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <RecipeCard recipe={recipe} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 animate-fadeIn">
            <ChefHat className={`h-16 w-16 ${colors.text.muted} mx-auto mb-4 animate-bounce`} />
            <h3 className={`text-lg font-medium ${colors.text.primary} mb-2`}>No recipes found</h3>
            <p className={`${colors.text.secondary} mb-6`}>
              {recipes.length === 0
                ? "Get started by adding your first recipe!"
                : "Try adjusting your search criteria or add a new recipe."
              }
            </p>
            <Link
              to="/add-recipe"
              className={`inline-flex items-center space-x-2 ${colors.button.primary} px-6 py-3 rounded-lg transition-all duration-300 hover-lift group`}
            >
              <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
              <span>Add Recipe</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Home;
