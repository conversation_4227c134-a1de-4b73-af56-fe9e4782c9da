import { useState, useEffect, useRef } from 'react';
import { Play, Pause, RotateCcw, Timer } from 'lucide-react';
import { useTheme } from '../context/ThemeContext';

const CookingTimer = ({ totalTime }) => {
  const [timeLeft, setTimeLeft] = useState(totalTime * 60); // Convert minutes to seconds
  const [isRunning, setIsRunning] = useState(false);
  const [isFinished, setIsFinished] = useState(false);
  const intervalRef = useRef(null);
  const { colors } = useTheme();

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            setIsRunning(false);
            setIsFinished(true);
            // Play notification sound (optional)
            if ('Notification' in window && Notification.permission === 'granted') {
              new Notification('🍳 Cooking Timer Finished!', {
                body: 'Your recipe is ready!',
                icon: '/favicon.ico'
              });
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStart = () => {
    setIsRunning(true);
    setIsFinished(false);
  };

  const handlePause = () => {
    setIsRunning(false);
  };

  const handleReset = () => {
    setIsRunning(false);
    setIsFinished(false);
    setTimeLeft(totalTime * 60);
  };

  const progressPercentage = ((totalTime * 60 - timeLeft) / (totalTime * 60)) * 100;

  return (
    <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6 text-center`}>
      <div className="flex items-center justify-center space-x-2 mb-4">
        <Timer className={`h-6 w-6 ${colors.text.accent}`} />
        <h3 className={`text-lg font-semibold ${colors.text.primary}`}>Cooking Timer</h3>
      </div>

      {/* Timer Display */}
      <div className="relative mb-6">
        <div className={`text-4xl font-bold ${isFinished ? 'text-green-500' : colors.text.primary} mb-2`}>
          {formatTime(timeLeft)}
        </div>
        <div className={`text-sm ${colors.text.secondary}`}>
          {isFinished ? '🎉 Ready!' : `${totalTime} min recipe`}
        </div>
        
        {/* Progress Ring */}
        <div className="relative w-32 h-32 mx-auto mt-4">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
            <circle
              cx="60"
              cy="60"
              r="54"
              stroke="currentColor"
              strokeWidth="8"
              fill="transparent"
              className={colors.text.muted}
              opacity="0.2"
            />
            <circle
              cx="60"
              cy="60"
              r="54"
              stroke={isFinished ? '#10b981' : '#3b82f6'}
              strokeWidth="8"
              fill="transparent"
              strokeDasharray={`${2 * Math.PI * 54}`}
              strokeDashoffset={`${2 * Math.PI * 54 * (1 - progressPercentage / 100)}`}
              className="transition-all duration-1000 ease-out"
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={`text-lg font-bold ${colors.text.primary}`}>
              {Math.round(progressPercentage)}%
            </span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex justify-center space-x-3">
        {!isRunning ? (
          <button
            onClick={handleStart}
            disabled={isFinished && timeLeft === 0}
            className={`flex items-center space-x-2 ${colors.button.primary} px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <Play className="h-4 w-4" />
            <span>{timeLeft === totalTime * 60 ? 'Start' : 'Resume'}</span>
          </button>
        ) : (
          <button
            onClick={handlePause}
            className={`flex items-center space-x-2 ${colors.button.secondary} px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105`}
          >
            <Pause className="h-4 w-4" />
            <span>Pause</span>
          </button>
        )}
        
        <button
          onClick={handleReset}
          className={`flex items-center space-x-2 ${colors.button.ghost} px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105`}
        >
          <RotateCcw className="h-4 w-4" />
          <span>Reset</span>
        </button>
      </div>

      {/* Status Messages */}
      {isFinished && (
        <div className="mt-4 p-3 bg-green-100 text-green-800 rounded-lg animate-pulse">
          🎉 Your recipe is ready to serve!
        </div>
      )}
      
      {isRunning && (
        <div className={`mt-4 p-3 ${colors.bg.secondary} ${colors.text.secondary} rounded-lg`}>
          ⏰ Timer is running...
        </div>
      )}
    </div>
  );
};

export default CookingTimer;
