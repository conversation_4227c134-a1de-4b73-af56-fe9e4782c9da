@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    min-height: 100vh;
    transition: all 0.3s ease;
  }

  /* Dark theme (default) */
  .dark body {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    color: #ffffff;
  }

  /* Light theme */
  .light body {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    color: #1f2937;
  }

  * {
    scrollbar-width: thin;
  }

  .dark * {
    scrollbar-color: #333333 #000000;
  }

  .light * {
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #000000;
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #333333, #666666);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #666666, #999999);
}

/* Light theme scrollbar */
.light ::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.light ::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #cbd5e1, #94a3b8);
  border-radius: 4px;
}

.light ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #94a3b8, #64748b);
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Dynamic animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.5s ease-out;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Glassmorphism effect */
.glass {
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.dark .glass {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.light .glass {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-light {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds */
.dark .gradient-primary {
  background: linear-gradient(135deg, #ffffff 0%, #e5e5e5 100%);
}

.dark .gradient-secondary {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
}

.dark .gradient-accent {
  background: linear-gradient(135deg, #ffffff 0%, #000000 100%);
}

.light .gradient-primary {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.light .gradient-secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.light .gradient-accent {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Modern Search Bar Effects */
.search-glow {
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1),
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.search-glow:focus-within {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1),
              0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.light .search-glow {
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1),
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.light .search-glow:focus-within {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1),
              0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Floating animation for search elements */
@keyframes float-search {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.float-search {
  animation: float-search 3s ease-in-out infinite;
}

/* Neon effects */
.neon-white {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3),
              0 0 40px rgba(255, 255, 255, 0.2),
              0 0 60px rgba(255, 255, 255, 0.1);
}

.neon-glow {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5),
              inset 0 0 20px rgba(255, 255, 255, 0.1);
}

/* Text effects */
.text-glow {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5),
               0 0 20px rgba(255, 255, 255, 0.3),
               0 0 30px rgba(255, 255, 255, 0.2);
}

.text-shadow-dark {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Card effects */
.card-dark {
  background: linear-gradient(145deg, #1a1a1a, #0d0d0d);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-light {
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
}
