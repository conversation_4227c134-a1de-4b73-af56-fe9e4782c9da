import { useAuth } from '../context/AuthContext';
import { useRecipes } from '../context/RecipeContext';
import RecipeCard from '../components/RecipeCard';
import { Heart, ChefHat } from 'lucide-react';
import { Link } from 'react-router-dom';

const Favorites = () => {
  const { user } = useAuth();
  const { recipes } = useRecipes();

  const favoriteRecipes = recipes.filter(recipe => 
    user?.favorites?.includes(recipe.id)
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8 animate-fadeIn">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Heart className="h-8 w-8 text-red-500 animate-pulse" />
            <h1 className="text-4xl md:text-5xl font-bold text-white text-glow">
              Your Favorite Recipes
            </h1>
            <Heart className="h-8 w-8 text-red-500 animate-pulse" />
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            All your saved recipes in one place
          </p>
        </div>

        {/* Favorites Grid */}
        {favoriteRecipes.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favoriteRecipes.map((recipe, index) => (
              <div 
                key={recipe.id} 
                className="animate-fadeIn"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <RecipeCard recipe={recipe} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 animate-fadeIn">
            <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No favorite recipes yet</h3>
            <p className="text-gray-300 mb-6">
              Start exploring recipes and add them to your favorites!
            </p>
            <Link
              to="/"
              className="inline-flex items-center space-x-2 bg-white text-black px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 hover-lift group neon-white"
            >
              <ChefHat className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span>Explore Recipes</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Favorites;
