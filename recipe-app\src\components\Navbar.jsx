import { Link, useLocation } from 'react-router-dom';
import { ChefHat, Home, Plus, User, LogOut, Heart } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import ThemeToggle from './ThemeToggle';

const Navbar = () => {
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();
  const { colors } = useTheme();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <nav className={`${colors.bg.glass} backdrop-blur-sm shadow-2xl ${colors.border.primary} sticky top-0 z-50 transition-all duration-300`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 group">
            <ChefHat className={`h-8 w-8 ${colors.text.primary} group-hover:rotate-12 transition-transform duration-300`} />
            <span className={`text-xl font-bold ${colors.text.primary} group-hover:scale-105 transition-transform duration-300`}>
              RecipeApp
            </span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 group ${
                isActive('/')
                  ? `${colors.text.primary} ${colors.bg.secondary} shadow-sm`
                  : `${colors.button.ghost}`
              }`}
            >
              <Home className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
              <span>Home</span>
            </Link>

            {isAuthenticated && (
              <>
                <Link
                  to="/add-recipe"
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 group ${
                    isActive('/add-recipe')
                      ? `${colors.text.primary} ${colors.bg.secondary} shadow-sm`
                      : `${colors.button.ghost}`
                  }`}
                >
                  <Plus className="h-4 w-4 group-hover:rotate-90 transition-transform duration-300" />
                  <span>Add Recipe</span>
                </Link>

                <Link
                  to="/favorites"
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 group ${
                    isActive('/favorites')
                      ? `${colors.text.primary} ${colors.bg.secondary} shadow-sm`
                      : `${colors.button.ghost}`
                  }`}
                >
                  <Heart className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                  <span>Favorites</span>
                </Link>
              </>
            )}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle />

            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <img
                    src={user?.avatar}
                    alt={user?.name}
                    className="w-8 h-8 rounded-full border-2 border-white/20"
                  />
                  <span className={`${colors.text.primary} text-sm font-medium hidden sm:block`}>
                    {user?.name}
                  </span>
                </div>
                <button
                  onClick={logout}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium ${colors.button.ghost} transition-all duration-300 group`}
                >
                  <LogOut className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                  <span className="hidden sm:block">Logout</span>
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  to="/login"
                  className={`${colors.button.ghost} transition-colors duration-300`}
                >
                  Login
                </Link>
                <Link
                  to="/signup"
                  className={`${colors.button.primary} px-4 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105`}
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button className="text-gray-600 hover:text-primary-600 p-2">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden border-t border-gray-200">
        <div className="px-2 pt-2 pb-3 space-y-1">
          <Link
            to="/"
            className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium ${
              isActive('/')
                ? 'text-primary-600 bg-primary-50'
                : 'text-gray-600 hover:text-primary-600 hover:bg-gray-50'
            }`}
          >
            <Home className="h-5 w-5" />
            <span>Home</span>
          </Link>
          
          <Link
            to="/add-recipe"
            className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium ${
              isActive('/add-recipe')
                ? 'text-primary-600 bg-primary-50'
                : 'text-gray-600 hover:text-primary-600 hover:bg-gray-50'
            }`}
          >
            <Plus className="h-5 w-5" />
            <span>Add Recipe</span>
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
