import { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  users: [], // In a real app, this would be handled by a backend
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  SIGNUP_SUCCESS: 'SIGNUP_SUCCESS',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_USERS: 'SET_USERS',
};

// Reducer function
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false,
        error: null,
      };
    case AUTH_ACTIONS.SIGNUP_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false,
        error: null,
        users: [...state.users, action.payload],
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      };
    case AUTH_ACTIONS.SET_USERS:
      return {
        ...state,
        users: action.payload,
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Context provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('recipeApp_user');
    const savedUsers = localStorage.getItem('recipeApp_users');
    
    if (savedUsers) {
      dispatch({ type: AUTH_ACTIONS.SET_USERS, payload: JSON.parse(savedUsers) });
    }
    
    if (savedUser) {
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: JSON.parse(savedUser) });
    }
  }, []);

  // Save users to localStorage whenever users array changes
  useEffect(() => {
    if (state.users.length > 0) {
      localStorage.setItem('recipeApp_users', JSON.stringify(state.users));
    }
  }, [state.users]);

  // Actions
  const login = async (email, password) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user in stored users
      const user = state.users.find(u => u.email === email && u.password === password);
      
      if (!user) {
        throw new Error('Invalid email or password');
      }

      const userWithoutPassword = { ...user };
      delete userWithoutPassword.password;

      localStorage.setItem('recipeApp_user', JSON.stringify(userWithoutPassword));
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: userWithoutPassword });
      
      return { success: true };
    } catch (error) {
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message });
      return { success: false, error: error.message };
    }
  };

  const signup = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      const existingUser = state.users.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser = {
        id: Date.now().toString(),
        ...userData,
        createdAt: new Date().toISOString(),
        favorites: [],
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=000000&color=ffffff&size=200`,
      };

      const userWithoutPassword = { ...newUser };
      delete userWithoutPassword.password;

      localStorage.setItem('recipeApp_user', JSON.stringify(userWithoutPassword));
      dispatch({ type: AUTH_ACTIONS.SIGNUP_SUCCESS, payload: newUser });
      
      return { success: true };
    } catch (error) {
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message });
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    localStorage.removeItem('recipeApp_user');
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
  };

  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  const addToFavorites = (recipeId) => {
    if (state.user) {
      const updatedUser = {
        ...state.user,
        favorites: [...(state.user.favorites || []), recipeId],
      };
      localStorage.setItem('recipeApp_user', JSON.stringify(updatedUser));
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: updatedUser });
    }
  };

  const removeFromFavorites = (recipeId) => {
    if (state.user) {
      const updatedUser = {
        ...state.user,
        favorites: (state.user.favorites || []).filter(id => id !== recipeId),
      };
      localStorage.setItem('recipeApp_user', JSON.stringify(updatedUser));
      dispatch({ type: AUTH_ACTIONS.LOGIN_SUCCESS, payload: updatedUser });
    }
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      signup,
      logout,
      clearError,
      addToFavorites,
      removeFromFavorites,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
