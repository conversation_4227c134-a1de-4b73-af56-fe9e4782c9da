import React from 'react';
import { Search, Filter } from 'lucide-react';
import { useRecipes } from '../context/RecipeContext';

const SearchBar = () => {
  const { searchTerm, selectedCategory, setSearchTerm, setCategory } = useRecipes();

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'breakfast', label: 'Breakfast' },
    { value: 'lunch', label: 'Lunch' },
    { value: 'dinner', label: 'Dinner' },
    { value: 'dessert', label: 'Dessert' },
    { value: 'snack', label: 'Snack' },
    { value: 'appetizer', label: 'Appetizer' },
  ];

  return (
    <div className="glass-dark rounded-lg shadow-2xl hover:shadow-xl transition-shadow duration-300 p-6 mb-8 animate-fadeIn border border-white/10">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search recipes, ingredients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-3 border border-white/20 rounded-lg leading-5 bg-black/20 placeholder-gray-400 text-white focus:outline-none focus:placeholder-gray-300 focus:ring-2 focus:ring-white/30 focus:border-white/40 text-sm transition-all duration-300 hover:border-white/30"
          />
        </div>

        {/* Category Filter */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter className="h-5 w-5 text-gray-400" />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setCategory(e.target.value)}
            className="block w-full pl-10 pr-8 py-3 border border-white/20 rounded-lg bg-black/20 text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 text-sm appearance-none cursor-pointer min-w-[200px]"
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value} className="bg-black text-white">
                {category.label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      {/* Search Results Info */}
      {searchTerm && (
        <div className="mt-4 text-sm text-gray-300">
          <span>Searching for: </span>
          <span className="font-medium text-white">"{searchTerm}"</span>
          {selectedCategory !== 'all' && (
            <>
              <span> in </span>
              <span className="font-medium text-white">
                {categories.find(cat => cat.value === selectedCategory)?.label}
              </span>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
