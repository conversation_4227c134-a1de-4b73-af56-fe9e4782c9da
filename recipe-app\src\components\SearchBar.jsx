import React from 'react';
import { Search, Filter } from 'lucide-react';
import { useRecipes } from '../context/RecipeContext';
import { useTheme } from '../context/ThemeContext';

const SearchBar = () => {
  const { searchTerm, selectedCategory, setSearchTerm, setCategory } = useRecipes();
  const { colors } = useTheme();

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'breakfast', label: 'Breakfast' },
    { value: 'lunch', label: 'Lunch' },
    { value: 'dinner', label: 'Dinner' },
    { value: 'dessert', label: 'Dessert' },
    { value: 'snack', label: 'Snack' },
    { value: 'appetizer', label: 'Appetizer' },
  ];

  return (
    <div className={`${colors.effects.glass} rounded-2xl shadow-2xl hover:shadow-xl transition-all duration-500 p-8 mb-8 animate-fadeIn ${colors.border.primary} backdrop-blur-xl`}>
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Modern Search Input */}
        <div className="flex-1 relative group">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
            <Search className={`h-5 w-5 transition-colors duration-300 ${searchTerm ? colors.text.accent : colors.text.muted} group-focus-within:${colors.text.accent}`} />
          </div>
          <input
            type="text"
            placeholder="🔍 Search for delicious recipes, ingredients, or cuisines..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`
              block w-full pl-12 pr-4 py-4
              ${colors.input.border} rounded-xl leading-5
              ${colors.input.bg} ${colors.input.placeholder} ${colors.input.text}
              focus:outline-none ${colors.input.focus}
              text-base font-medium
              transition-all duration-300
              hover:shadow-lg focus:shadow-xl
              backdrop-blur-sm
              group-hover:scale-[1.02] focus:scale-[1.02]
              placeholder:font-normal placeholder:text-base
            `}
          />
          {/* Search highlight effect */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        </div>

        {/* Modern Category Filter */}
        <div className="relative group min-w-[240px]">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
            <Filter className={`h-5 w-5 transition-colors duration-300 ${colors.text.muted} group-focus-within:${colors.text.accent}`} />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setCategory(e.target.value)}
            className={`
              block w-full pl-12 pr-10 py-4
              ${colors.input.border} rounded-xl
              ${colors.input.bg} ${colors.input.text}
              focus:outline-none ${colors.input.focus}
              text-base font-medium appearance-none cursor-pointer
              transition-all duration-300
              hover:shadow-lg focus:shadow-xl
              backdrop-blur-sm
              group-hover:scale-[1.02] focus:scale-[1.02]
            `}
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value} className={`${colors.input.bg} ${colors.input.text} py-2`}>
                {category.label}
              </option>
            ))}
          </select>
          {/* Custom dropdown arrow */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
            <svg className={`h-5 w-5 transition-transform duration-300 ${colors.text.muted} group-focus-within:rotate-180`} viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          {/* Filter highlight effect */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/10 to-blue-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        </div>
      </div>

      {/* Modern Search Results Info */}
      {searchTerm && (
        <div className={`mt-6 p-4 rounded-xl ${colors.bg.secondary} ${colors.border.primary} animate-fadeIn`}>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className={`text-sm ${colors.text.secondary}`}>Searching for: </span>
            <span className={`font-semibold ${colors.text.accent} bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
              "{searchTerm}"
            </span>
            {selectedCategory !== 'all' && (
              <>
                <span className={`text-sm ${colors.text.secondary}`}> in </span>
                <span className={`font-semibold ${colors.text.accent} px-2 py-1 rounded-lg ${colors.bg.card} ${colors.border.primary}`}>
                  {categories.find(cat => cat.value === selectedCategory)?.label}
                </span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
