import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useRecipes } from '../context/RecipeContext';
import RecipeForm from '../components/RecipeForm';

const EditRecipe = () => {
  const { id } = useParams();
  const { recipes } = useRecipes();

  const recipe = recipes.find(r => r.id === id);

  if (!recipe) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <RecipeForm recipe={recipe} isEditing={true} />
      </div>
    </div>
  );
};

export default EditRecipe;
