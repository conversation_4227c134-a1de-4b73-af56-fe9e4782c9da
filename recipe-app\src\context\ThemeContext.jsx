import { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDark, setIsDark] = useState(() => {
    const saved = localStorage.getItem('recipeApp_theme');
    return saved ? JSON.parse(saved) : true; // Default to dark theme
  });

  useEffect(() => {
    localStorage.setItem('recipeApp_theme', JSON.stringify(isDark));
    
    // Update document class for global styling
    if (isDark) {
      document.documentElement.classList.add('dark');
      document.documentElement.classList.remove('light');
    } else {
      document.documentElement.classList.add('light');
      document.documentElement.classList.remove('dark');
    }
  }, [isDark]);

  const toggleTheme = () => {
    setIsDark(!isDark);
  };

  const theme = {
    isDark,
    isLight: !isDark,
    toggleTheme,
    colors: {
      // Background colors
      bg: {
        primary: isDark ? 'from-black via-gray-900 to-black' : 'from-white via-gray-50 to-gray-100',
        secondary: isDark ? 'bg-gray-900' : 'bg-gray-50',
        card: isDark ? 'bg-gray-800/50' : 'bg-white',
        glass: isDark ? 'bg-black/20' : 'bg-white/80',
      },
      // Text colors - Fixed for proper contrast
      text: {
        primary: isDark ? 'text-white' : 'text-gray-900',
        secondary: isDark ? 'text-gray-300' : 'text-gray-600',
        muted: isDark ? 'text-gray-400' : 'text-gray-500',
        accent: isDark ? 'text-white' : 'text-gray-900',
        inverse: isDark ? 'text-gray-900' : 'text-white', // For buttons and highlights
      },
      // Border colors
      border: {
        primary: isDark ? 'border-white/10' : 'border-gray-200',
        secondary: isDark ? 'border-white/20' : 'border-gray-300',
        focus: isDark ? 'border-white/40' : 'border-blue-500',
      },
      // Button colors - Enhanced for better contrast
      button: {
        primary: isDark ? 'bg-white text-black hover:bg-gray-100 hover:shadow-lg' : 'bg-gray-900 text-white hover:bg-gray-800 hover:shadow-lg',
        secondary: isDark ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-900 hover:bg-gray-300',
        ghost: isDark ? 'text-gray-300 hover:text-white hover:bg-white/10' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100',
        accent: isDark ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700' : 'bg-gradient-to-r from-blue-600 to-purple-700 text-white hover:from-blue-700 hover:to-purple-800',
      },
      // Input colors
      input: {
        bg: isDark ? 'bg-black/20' : 'bg-white',
        border: isDark ? 'border-white/20' : 'border-gray-300',
        focus: isDark ? 'focus:border-white/40 focus:ring-white/30' : 'focus:border-blue-500 focus:ring-blue-500/20',
        placeholder: isDark ? 'placeholder-gray-400' : 'placeholder-gray-500',
        text: isDark ? 'text-white' : 'text-gray-900',
      },
      // Special effects
      effects: {
        glow: isDark ? 'neon-white' : 'shadow-lg',
        hover: isDark ? 'hover:shadow-white/10' : 'hover:shadow-gray-300/50',
        glass: isDark ? 'glass-dark' : 'glass-light',
      }
    }
  };

  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
};
