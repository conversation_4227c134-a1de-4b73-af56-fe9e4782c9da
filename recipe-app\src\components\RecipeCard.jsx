import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, Users, Edit, Trash2, Star, Heart } from 'lucide-react';
import { useRecipes } from '../context/RecipeContext';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';

const RecipeCard = ({ recipe }) => {
  const { deleteRecipe } = useRecipes();
  const { user, isAuthenticated, addToFavorites, removeFromFavorites } = useAuth();
  const { colors } = useTheme();

  const isFavorite = user?.favorites?.includes(recipe.id);

  const handleDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this recipe?')) {
      deleteRecipe(recipe.id);
    }
  };

  const handleFavorite = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isAuthenticated) return;

    if (isFavorite) {
      removeFromFavorites(recipe.id);
    } else {
      addToFavorites(recipe.id);
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      breakfast: 'bg-yellow-100 text-yellow-800',
      lunch: 'bg-green-100 text-green-800',
      dinner: 'bg-blue-100 text-blue-800',
      dessert: 'bg-pink-100 text-pink-800',
      snack: 'bg-purple-100 text-purple-800',
      appetizer: 'bg-orange-100 text-orange-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`${colors.bg.card} rounded-xl shadow-2xl hover:shadow-xl transition-all duration-300 overflow-hidden group hover-lift animate-fadeIn ${colors.border.primary}`}>
      <Link to={`/recipe/${recipe.id}`} className="block">
        {/* Image */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={recipe.image || '/api/placeholder/400/300'}
            alt={recipe.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 ease-out"
            onError={(e) => {
              e.target.src = 'https://images.unsplash.com/photo-1546548970-71785318a17b?w=400&h=300&fit=crop';
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="absolute top-3 left-3 animate-slideIn">
            <span className={`px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm ${getCategoryColor(recipe.category)} shadow-lg`}>
              {recipe.category}
            </span>
          </div>
          {recipe.rating && (
            <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg animate-slideIn">
              <Star className="h-3 w-3 text-yellow-400 fill-current animate-pulse" />
              <span className="text-xs font-medium">{recipe.rating}</span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          <h3 className={`text-lg font-semibold ${colors.text.primary} mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors`}>
            {recipe.title}
          </h3>

          <p className={`${colors.text.secondary} text-sm mb-3 line-clamp-2`}>
            {recipe.description}
          </p>

          {/* Recipe Info */}
          <div className={`flex items-center justify-between text-sm ${colors.text.muted} mb-3`}>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{recipe.cookTime || '30'} min</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{recipe.servings || '4'} servings</span>
              </div>
            </div>
          </div>

          {/* Ingredients Preview */}
          <div className="mb-3">
            <p className={`text-xs ${colors.text.muted} mb-1`}>Ingredients:</p>
            <div className="flex flex-wrap gap-1">
              {recipe.ingredients?.slice(0, 3).map((ingredient, index) => (
                <span
                  key={index}
                  className={`${colors.bg.secondary} ${colors.text.secondary} px-2 py-1 rounded-full text-xs ${colors.border.primary}`}
                >
                  {ingredient.length > 15 ? `${ingredient.substring(0, 15)}...` : ingredient}
                </span>
              ))}
              {recipe.ingredients?.length > 3 && (
                <span className={`text-xs ${colors.text.muted} px-2 py-1`}>
                  +{recipe.ingredients.length - 3} more
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>

      {/* Action Buttons */}
      <div className={`px-4 pb-4 flex justify-between items-center border-t ${colors.border.primary} pt-3`}>
        <Link
          to={`/recipe/${recipe.id}`}
          className={`${colors.text.accent} hover:text-blue-600 text-sm font-medium transition-colors`}
        >
          View Recipe
        </Link>
        <div className="flex space-x-2">
          {isAuthenticated && (
            <button
              onClick={handleFavorite}
              className={`p-2 transition-colors ${
                isFavorite
                  ? 'text-red-500 hover:text-red-400'
                  : 'text-gray-400 hover:text-red-500'
              }`}
            >
              <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
            </button>
          )}
          <Link
            to={`/edit-recipe/${recipe.id}`}
            className={`p-2 ${colors.text.muted} hover:${colors.text.primary} transition-colors`}
            onClick={(e) => e.stopPropagation()}
          >
            <Edit className="h-4 w-4" />
          </Link>
          <button
            onClick={handleDelete}
            className={`p-2 ${colors.text.muted} hover:text-red-500 transition-colors`}
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default RecipeCard;
