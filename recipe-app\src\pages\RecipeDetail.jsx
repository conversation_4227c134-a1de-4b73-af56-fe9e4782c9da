import React from 'react';
import { usePara<PERSON>, Link, Navigate } from 'react-router-dom';
import { useRecipes } from '../context/RecipeContext';
import { useTheme } from '../context/ThemeContext';
import { Clock, Users, Star, Edit, Trash2, ArrowLeft, ChefHat, Timer, UtensilsCrossed } from 'lucide-react';
import CookingTimer from '../components/CookingTimer';

const RecipeDetail = () => {
  const { id } = useParams();
  const { recipes, deleteRecipe } = useRecipes();
  const { colors } = useTheme();

  const recipe = recipes.find(r => r.id === id);

  if (!recipe) {
    return <Navigate to="/" replace />;
  }

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this recipe?')) {
      deleteRecipe(recipe.id);
      // Navigate will happen automatically due to recipe not existing
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      breakfast: 'bg-yellow-100 text-yellow-800',
      lunch: 'bg-green-100 text-green-800',
      dinner: 'bg-blue-100 text-blue-800',
      dessert: 'bg-pink-100 text-pink-800',
      snack: 'bg-purple-100 text-purple-800',
      appetizer: 'bg-orange-100 text-orange-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      easy: 'text-green-600',
      medium: 'text-yellow-600',
      hard: 'text-red-600',
    };
    return colors[difficulty] || 'text-gray-600';
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br ${colors.bg.primary}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link
          to="/"
          className={`inline-flex items-center space-x-2 ${colors.button.ghost} mb-6 transition-all duration-300 hover:scale-105`}
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Recipes</span>
        </Link>

        <div className={`${colors.bg.card} rounded-xl shadow-2xl overflow-hidden ${colors.border.primary} backdrop-blur-xl`}>
          {/* Hero Image */}
          <div className="relative h-64 md:h-80">
            <img
              src={recipe.image || 'https://images.unsplash.com/photo-1546548970-71785318a17b?w=800&h=400&fit=crop'}
              alt={recipe.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.src = 'https://images.unsplash.com/photo-1546548970-71785318a17b?w=800&h=400&fit=crop';
              }}
            />
            <div className="absolute top-4 left-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm ${getCategoryColor(recipe.category)} shadow-lg`}>
                {recipe.category}
              </span>
            </div>
            {recipe.rating && (
              <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm font-medium">{recipe.rating}</span>
              </div>
            )}

            {/* Quick Info Overlay */}
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-black/50 backdrop-blur-md rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Timer className="h-5 w-5 text-blue-400" />
                      <span className="font-semibold">{recipe.cookTime} min</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <UtensilsCrossed className="h-5 w-5 text-green-400" />
                      <span className="font-semibold">Serves {recipe.servings}</span>
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    recipe.difficulty === 'easy' ? 'bg-green-500/20 text-green-300' :
                    recipe.difficulty === 'medium' ? 'bg-yellow-500/20 text-yellow-300' :
                    'bg-red-500/20 text-red-300'
                  }`}>
                    {recipe.difficulty}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex-1">
                <h1 className={`text-3xl md:text-4xl font-bold ${colors.text.primary} mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
                  {recipe.title}
                </h1>
                <p className={`${colors.text.secondary} text-lg mb-4 leading-relaxed`}>
                  {recipe.description}
                </p>

                {/* Quick Stats Bar */}
                <div className={`inline-flex items-center space-x-6 ${colors.bg.secondary} ${colors.border.primary} rounded-lg px-4 py-2`}>
                  <div className="flex items-center space-x-2">
                    <Clock className={`h-4 w-4 ${colors.text.accent}`} />
                    <span className={`text-sm font-medium ${colors.text.primary}`}>
                      {recipe.cookTime} minutes
                    </span>
                  </div>
                  <div className="w-px h-4 bg-gray-300"></div>
                  <div className="flex items-center space-x-2">
                    <Users className={`h-4 w-4 ${colors.text.accent}`} />
                    <span className={`text-sm font-medium ${colors.text.primary}`}>
                      {recipe.servings} servings
                    </span>
                  </div>
                  <div className="w-px h-4 bg-gray-300"></div>
                  <div className="flex items-center space-x-2">
                    <ChefHat className={`h-4 w-4 ${colors.text.accent}`} />
                    <span className={`text-sm font-medium capitalize ${getDifficultyColor(recipe.difficulty)}`}>
                      {recipe.difficulty}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2 ml-4">
                <Link
                  to={`/edit-recipe/${recipe.id}`}
                  className={`p-3 ${colors.button.ghost} rounded-lg transition-all duration-300 hover:scale-110`}
                >
                  <Edit className="h-5 w-5" />
                </Link>
                <button
                  onClick={handleDelete}
                  className={`p-3 ${colors.text.muted} hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-300 hover:scale-110`}
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Enhanced Recipe Info Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Cooking Time - Enhanced */}
              <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl`}>
                <div className="relative">
                  <Clock className={`h-10 w-10 ${colors.text.accent} mx-auto mb-3 animate-pulse`} />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-ping"></div>
                </div>
                <p className={`text-sm ${colors.text.secondary} mb-1 font-medium`}>Total Cook Time</p>
                <p className={`text-2xl font-bold ${colors.text.primary} mb-1`}>{recipe.cookTime}</p>
                <p className={`text-xs ${colors.text.muted} uppercase tracking-wide`}>Minutes</p>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-1">
                  <div
                    className="bg-blue-500 h-1 rounded-full transition-all duration-1000"
                    style={{ width: `${Math.min((recipe.cookTime / 60) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              {/* Servings - Enhanced */}
              <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl`}>
                <div className="relative">
                  <Users className={`h-10 w-10 ${colors.text.accent} mx-auto mb-3 animate-pulse`} />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-ping"></div>
                </div>
                <p className={`text-sm ${colors.text.secondary} mb-1 font-medium`}>Serves</p>
                <p className={`text-2xl font-bold ${colors.text.primary} mb-1`}>{recipe.servings}</p>
                <p className={`text-xs ${colors.text.muted} uppercase tracking-wide`}>People</p>
                <div className="mt-2 flex justify-center space-x-1">
                  {[...Array(Math.min(recipe.servings, 8))].map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}></div>
                  ))}
                  {recipe.servings > 8 && <span className={`text-xs ${colors.text.muted}`}>+</span>}
                </div>
              </div>

              {/* Difficulty */}
              <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl`}>
                <div className="relative">
                  <ChefHat className={`h-10 w-10 ${colors.text.accent} mx-auto mb-3`} />
                </div>
                <p className={`text-sm ${colors.text.secondary} mb-1 font-medium`}>Difficulty</p>
                <p className={`text-lg font-bold capitalize ${getDifficultyColor(recipe.difficulty)} mb-1`}>
                  {recipe.difficulty}
                </p>
                <div className="flex justify-center space-x-1 mt-2">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-2 h-2 rounded-full ${
                        i < (recipe.difficulty === 'easy' ? 1 : recipe.difficulty === 'medium' ? 2 : 3)
                          ? getDifficultyColor(recipe.difficulty).includes('green') ? 'bg-green-500' :
                            getDifficultyColor(recipe.difficulty).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'
                          : 'bg-gray-300'
                      }`}
                    ></div>
                  ))}
                </div>
              </div>

              {/* Rating */}
              {recipe.rating && (
                <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl`}>
                  <div className="relative">
                    <Star className={`h-10 w-10 text-yellow-500 mx-auto mb-3 fill-current`} />
                  </div>
                  <p className={`text-sm ${colors.text.secondary} mb-1 font-medium`}>Rating</p>
                  <p className={`text-2xl font-bold ${colors.text.primary} mb-1`}>{recipe.rating}</p>
                  <p className={`text-xs ${colors.text.muted} uppercase tracking-wide`}>Out of 5</p>
                  <div className="flex justify-center space-x-1 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-3 h-3 ${i < Math.floor(recipe.rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {/* Ingredients */}
              <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6`}>
                <h2 className={`text-2xl font-bold ${colors.text.primary} mb-6 flex items-center space-x-2`}>
                  <UtensilsCrossed className="h-6 w-6 text-green-500" />
                  <span>Ingredients</span>
                </h2>
                <ul className="space-y-3">
                  {recipe.ingredients?.map((ingredient, index) => (
                    <li key={index} className={`flex items-start space-x-3 p-2 rounded-lg hover:${colors.bg.secondary} transition-colors duration-200`}>
                      <span className="flex-shrink-0 w-3 h-3 bg-green-500 rounded-full mt-2 animate-pulse"></span>
                      <span className={`${colors.text.secondary} leading-relaxed`}>{ingredient}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Instructions */}
              <div className={`${colors.effects.glass} ${colors.border.primary} rounded-xl p-6`}>
                <h2 className={`text-2xl font-bold ${colors.text.primary} mb-6 flex items-center space-x-2`}>
                  <ChefHat className="h-6 w-6 text-blue-500" />
                  <span>Instructions</span>
                </h2>
                <ol className="space-y-4">
                  {recipe.instructions?.map((instruction, index) => (
                    <li key={index} className={`flex space-x-4 p-3 rounded-lg hover:${colors.bg.secondary} transition-colors duration-200`}>
                      <span className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                        {index + 1}
                      </span>
                      <p className={`${colors.text.secondary} pt-1 leading-relaxed`}>{instruction}</p>
                    </li>
                  ))}
                </ol>
              </div>

              {/* Cooking Timer */}
              <div className="lg:row-span-2">
                <CookingTimer totalTime={recipe.cookTime} />
              </div>
            </div>

            {/* Footer */}
            <div className={`mt-8 pt-6 border-t ${colors.border.primary} text-center`}>
              <p className={`text-sm ${colors.text.muted}`}>
                Created on {new Date(recipe.createdAt).toLocaleDateString()}
                {recipe.updatedAt !== recipe.createdAt && (
                  <span> • Updated on {new Date(recipe.updatedAt).toLocaleDateString()}</span>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipeDetail;
