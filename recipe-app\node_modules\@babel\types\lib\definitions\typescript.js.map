{"version": 3, "names": ["_utils", "require", "_core", "_is", "defineType", "defineAliasedType", "bool", "assertValueType", "tSFunctionTypeAnnotationCommon", "returnType", "validate", "assertNodeType", "optional", "typeParameters", "aliases", "visitor", "fields", "accessibility", "assertOneOf", "readonly", "parameter", "override", "decorators", "arrayOfType", "Object", "assign", "functionDeclaration<PERSON>ommon", "classMethodOrDeclareMethodCommon", "left", "validateType", "right", "signatureDeclarationCommon", "validateOptionalType", "validateArrayOfType", "callConstructSignatureDeclaration", "namedTypeElementCommon", "key", "computed", "default", "validateOptional", "typeAnnotation", "kind", "static", "parameters", "tsKeywordTypes", "type", "fnOrCtrBase", "abstract", "typeName", "builder", "parameterName", "asserts", "exprName", "members", "elementType", "elementTypes", "label", "unionOrIntersection", "types", "checkType", "extendsType", "trueType", "falseType", "typeParameter", "operator", "objectType", "indexType", "nameType", "quasis", "chain", "assertEach", "node", "val", "length", "TypeError", "literal", "unaryExpression", "unaryOperator", "validator", "parent", "is", "argument", "oneOfNodeTypes", "expression", "declare", "id", "extends", "body", "TSTypeExpression", "const", "initializer", "global", "qualifier", "options", "isExport", "moduleReference", "importKind", "params", "name", "in", "out", "constraint"], "sources": ["../../src/definitions/typescript.ts"], "sourcesContent": ["import type * as t from \"../index.ts\";\nimport {\n  defineAliasedType,\n  arrayOfType,\n  assertEach,\n  assertNodeType,\n  assertOneOf,\n  assertValueType,\n  chain,\n  validate,\n  validateArrayOfType,\n  validateOptional,\n  validateOptionalType,\n  validateType,\n  type Validator,\n} from \"./utils.ts\";\nimport {\n  functionDeclarationCommon,\n  classMethodOrDeclareMethodCommon,\n} from \"./core.ts\";\nimport is from \"../validators/is.ts\";\n\nconst defineType = defineAliasedType(\"TypeScript\");\n\nconst bool = assertValueType(\"boolean\");\n\nconst tSFunctionTypeAnnotationCommon = () => ({\n  returnType: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TSTypeAnnotation\")\n      : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        assertNodeType(\"TSTypeAnnotation\", \"Noop\"),\n    optional: true,\n  },\n  typeParameters: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TSTypeParameterDeclaration\")\n      : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        assertNodeType(\"TSTypeParameterDeclaration\", \"Noop\"),\n    optional: true,\n  },\n});\n\ndefineType(\"TSParameterProperty\", {\n  aliases: process.env.BABEL_8_BREAKING ? [] : [\"LVal\"],\n  visitor: [\"parameter\"],\n  fields: {\n    accessibility: {\n      validate: assertOneOf(\"public\", \"private\", \"protected\"),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    parameter: {\n      validate: assertNodeType(\"Identifier\", \"AssignmentPattern\"),\n    },\n    override: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    decorators: {\n      validate: arrayOfType(\"Decorator\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TSDeclareFunction\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"params\", \"returnType\"],\n  fields: {\n    ...functionDeclarationCommon(),\n    ...tSFunctionTypeAnnotationCommon(),\n  },\n});\n\ndefineType(\"TSDeclareMethod\", {\n  visitor: [\"decorators\", \"key\", \"typeParameters\", \"params\", \"returnType\"],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...tSFunctionTypeAnnotationCommon(),\n  },\n});\n\ndefineType(\"TSQualifiedName\", {\n  aliases: [\"TSEntityName\"],\n  visitor: [\"left\", \"right\"],\n  fields: {\n    left: validateType(\"TSEntityName\"),\n    right: validateType(\"Identifier\"),\n  },\n});\n\nconst signatureDeclarationCommon = () => ({\n  typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n  [process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\"]: validateArrayOfType(\n    \"ArrayPattern\",\n    \"Identifier\",\n    \"ObjectPattern\",\n    \"RestElement\",\n  ),\n  [process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\"]:\n    validateOptionalType(\"TSTypeAnnotation\"),\n});\n\nconst callConstructSignatureDeclaration = {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n  fields: signatureDeclarationCommon(),\n};\n\ndefineType(\"TSCallSignatureDeclaration\", callConstructSignatureDeclaration);\ndefineType(\n  \"TSConstructSignatureDeclaration\",\n  callConstructSignatureDeclaration,\n);\n\nconst namedTypeElementCommon = () => ({\n  key: validateType(\"Expression\"),\n  computed: { default: false },\n  optional: validateOptional(bool),\n});\n\ndefineType(\"TSPropertySignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\"key\", \"typeAnnotation\"],\n  fields: {\n    ...namedTypeElementCommon(),\n    readonly: validateOptional(bool),\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n    kind: {\n      optional: true,\n      validate: assertOneOf(\"get\", \"set\"),\n    },\n  },\n});\n\ndefineType(\"TSMethodSignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\n    \"key\",\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n  fields: {\n    ...signatureDeclarationCommon(),\n    ...namedTypeElementCommon(),\n    kind: {\n      validate: assertOneOf(\"method\", \"get\", \"set\"),\n    },\n  },\n});\n\ndefineType(\"TSIndexSignature\", {\n  aliases: [\"TSTypeElement\"],\n  visitor: [\"parameters\", \"typeAnnotation\"],\n  fields: {\n    readonly: validateOptional(bool),\n    static: validateOptional(bool),\n    parameters: validateArrayOfType(\"Identifier\"), // Length must be 1\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n  },\n});\n\nconst tsKeywordTypes = [\n  \"TSAnyKeyword\",\n  \"TSBooleanKeyword\",\n  \"TSBigIntKeyword\",\n  \"TSIntrinsicKeyword\",\n  \"TSNeverKeyword\",\n  \"TSNullKeyword\",\n  \"TSNumberKeyword\",\n  \"TSObjectKeyword\",\n  \"TSStringKeyword\",\n  \"TSSymbolKeyword\",\n  \"TSUndefinedKeyword\",\n  \"TSUnknownKeyword\",\n  \"TSVoidKeyword\",\n] as const;\n\nfor (const type of tsKeywordTypes) {\n  defineType(type, {\n    aliases: [\"TSType\", \"TSBaseType\"],\n    visitor: [],\n    fields: {},\n  });\n}\n\ndefineType(\"TSThisType\", {\n  aliases: [\"TSType\", \"TSBaseType\"],\n  visitor: [],\n  fields: {},\n});\n\nconst fnOrCtrBase = {\n  aliases: [\"TSType\"],\n  visitor: [\n    \"typeParameters\",\n    process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\",\n    process.env.BABEL_8_BREAKING ? \"returnType\" : \"typeAnnotation\",\n  ],\n};\n\ndefineType(\"TSFunctionType\", {\n  ...fnOrCtrBase,\n  fields: signatureDeclarationCommon(),\n});\ndefineType(\"TSConstructorType\", {\n  ...fnOrCtrBase,\n  fields: {\n    ...signatureDeclarationCommon(),\n    abstract: validateOptional(bool),\n  },\n});\n\ndefineType(\"TSTypeReference\", {\n  aliases: [\"TSType\"],\n  visitor: [\n    \"typeName\",\n    process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\",\n  ],\n  fields: {\n    typeName: validateType(\"TSEntityName\"),\n    [process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\"]:\n      validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSTypePredicate\", {\n  aliases: [\"TSType\"],\n  visitor: [\"parameterName\", \"typeAnnotation\"],\n  builder: [\"parameterName\", \"typeAnnotation\", \"asserts\"],\n  fields: {\n    parameterName: validateType(\"Identifier\", \"TSThisType\"),\n    typeAnnotation: validateOptionalType(\"TSTypeAnnotation\"),\n    asserts: validateOptional(bool),\n  },\n});\n\ndefineType(\"TSTypeQuery\", {\n  aliases: [\"TSType\"],\n  visitor: [\n    \"exprName\",\n    process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\",\n  ],\n  fields: {\n    exprName: validateType(\"TSEntityName\", \"TSImportType\"),\n    [process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\"]:\n      validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\ndefineType(\"TSTypeLiteral\", {\n  aliases: [\"TSType\"],\n  visitor: [\"members\"],\n  fields: {\n    members: validateArrayOfType(\"TSTypeElement\"),\n  },\n});\n\ndefineType(\"TSArrayType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"elementType\"],\n  fields: {\n    elementType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTupleType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"elementTypes\"],\n  fields: {\n    elementTypes: validateArrayOfType(\"TSType\", \"TSNamedTupleMember\"),\n  },\n});\n\ndefineType(\"TSOptionalType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSRestType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSNamedTupleMember\", {\n  visitor: [\"label\", \"elementType\"],\n  builder: [\"label\", \"elementType\", \"optional\"],\n  fields: {\n    label: validateType(\"Identifier\"),\n    optional: {\n      validate: bool,\n      default: false,\n    },\n    elementType: validateType(\"TSType\"),\n  },\n});\n\nconst unionOrIntersection = {\n  aliases: [\"TSType\"],\n  visitor: [\"types\"],\n  fields: {\n    types: validateArrayOfType(\"TSType\"),\n  },\n};\n\ndefineType(\"TSUnionType\", unionOrIntersection);\ndefineType(\"TSIntersectionType\", unionOrIntersection);\n\ndefineType(\"TSConditionalType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"checkType\", \"extendsType\", \"trueType\", \"falseType\"],\n  fields: {\n    checkType: validateType(\"TSType\"),\n    extendsType: validateType(\"TSType\"),\n    trueType: validateType(\"TSType\"),\n    falseType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSInferType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeParameter\"],\n  fields: {\n    typeParameter: validateType(\"TSTypeParameter\"),\n  },\n});\n\ndefineType(\"TSParenthesizedType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTypeOperator\", {\n  aliases: [\"TSType\"],\n  visitor: [\"typeAnnotation\"],\n  builder: [\"typeAnnotation\", \"operator\"],\n  fields: {\n    operator: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertOneOf(\"keyof\", \"readonly\", \"unique\")\n        : assertValueType(\"string\"),\n    },\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSIndexedAccessType\", {\n  aliases: [\"TSType\"],\n  visitor: [\"objectType\", \"indexType\"],\n  fields: {\n    objectType: validateType(\"TSType\"),\n    indexType: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSMappedType\", {\n  aliases: [\"TSType\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"key\", \"constraint\", \"nameType\", \"typeAnnotation\"]\n    : [\"typeParameter\", \"nameType\", \"typeAnnotation\"],\n  builder: process.env.BABEL_8_BREAKING\n    ? [\"key\", \"constraint\", \"nameType\", \"typeAnnotation\"]\n    : [\"typeParameter\", \"typeAnnotation\", \"nameType\"],\n  fields: {\n    ...(process.env.BABEL_8_BREAKING\n      ? {\n          key: validateType(\"Identifier\"),\n          constraint: validateType(\"TSType\"),\n        }\n      : {\n          typeParameter: validateType(\"TSTypeParameter\"),\n        }),\n    readonly: validateOptional(assertOneOf(true, false, \"+\", \"-\")),\n    optional: validateOptional(assertOneOf(true, false, \"+\", \"-\")),\n    typeAnnotation: validateOptionalType(\"TSType\"),\n    nameType: validateOptionalType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTemplateLiteralType\", {\n  aliases: [\"TSType\", \"TSBaseType\"],\n  visitor: [\"quasis\", \"types\"],\n  fields: {\n    quasis: validateArrayOfType(\"TemplateElement\"),\n    types: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"TSType\")),\n        function (node: t.TSTemplateLiteralType, key, val) {\n          if (node.quasis.length !== val.length + 1) {\n            throw new TypeError(\n              `Number of ${\n                node.type\n              } quasis should be exactly one more than the number of types.\\nExpected ${\n                val.length + 1\n              } quasis but got ${node.quasis.length}`,\n            );\n          }\n        } as Validator,\n      ),\n    },\n  },\n});\n\ndefineType(\"TSLiteralType\", {\n  aliases: [\"TSType\", \"TSBaseType\"],\n  visitor: [\"literal\"],\n  fields: {\n    literal: {\n      validate: (function () {\n        const unaryExpression = assertNodeType(\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const unaryOperator = assertOneOf(\"-\");\n\n        const literal = assertNodeType(\n          \"NumericLiteral\",\n          \"StringLiteral\",\n          \"BooleanLiteral\",\n          \"BigIntLiteral\",\n          \"TemplateLiteral\",\n        );\n        function validator(parent: any, key: string, node: any) {\n          // type A = -1 | 1;\n          if (is(\"UnaryExpression\", node)) {\n            // check operator first\n            unaryOperator(node, \"operator\", node.operator);\n            unaryExpression(node, \"argument\", node.argument);\n          } else {\n            // type A = 'foo' | 'bar' | false | 1;\n            literal(parent, key, node);\n          }\n        }\n\n        validator.oneOfNodeTypes = [\n          \"NumericLiteral\",\n          \"StringLiteral\",\n          \"BooleanLiteral\",\n          \"BigIntLiteral\",\n          \"TemplateLiteral\",\n          \"UnaryExpression\",\n        ];\n\n        return validator;\n      })(),\n    },\n  },\n});\n\nif (process.env.BABEL_8_BREAKING) {\n  defineType(\"TSClassImplements\", {\n    aliases: [\"TSType\"],\n    visitor: [\"expression\", \"typeArguments\"],\n    fields: {\n      expression: validateType(\"Expression\"),\n      typeArguments: validateOptionalType(\"TSTypeParameterInstantiation\"),\n    },\n  });\n  defineType(\"TSInterfaceHeritage\", {\n    aliases: [\"TSType\"],\n    visitor: [\"expression\", \"typeArguments\"],\n    fields: {\n      expression: validateType(\"Expression\"),\n      typeArguments: validateOptionalType(\"TSTypeParameterInstantiation\"),\n    },\n  });\n} else {\n  defineType(\"TSExpressionWithTypeArguments\", {\n    aliases: [\"TSType\"],\n    visitor: [\"expression\", \"typeParameters\"],\n    fields: {\n      expression: validateType(\"TSEntityName\"),\n      typeParameters: validateOptionalType(\"TSTypeParameterInstantiation\"),\n    },\n  });\n}\n\ndefineType(\"TSInterfaceDeclaration\", {\n  // \"Statement\" alias prevents a semicolon from appearing after it in an export declaration.\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"extends\", \"body\"],\n  fields: {\n    declare: validateOptional(bool),\n    id: validateType(\"Identifier\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n    extends: validateOptional(\n      arrayOfType(\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        process.env.BABEL_8_BREAKING\n          ? \"TSClassImplements\"\n          : \"TSExpressionWithTypeArguments\",\n      ),\n    ),\n    body: validateType(\"TSInterfaceBody\"),\n  },\n});\n\ndefineType(\"TSInterfaceBody\", {\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\"TSTypeElement\"),\n  },\n});\n\ndefineType(\"TSTypeAliasDeclaration\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"typeParameters\", \"typeAnnotation\"],\n  fields: {\n    declare: validateOptional(bool),\n    id: validateType(\"Identifier\"),\n    typeParameters: validateOptionalType(\"TSTypeParameterDeclaration\"),\n    typeAnnotation: validateType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSInstantiationExpression\", {\n  aliases: [\"Expression\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"expression\", \"typeArguments\"]\n    : [\"expression\", \"typeParameters\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n    [process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\"]:\n      validateOptionalType(\"TSTypeParameterInstantiation\"),\n  },\n});\n\nconst TSTypeExpression = {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"expression\", \"typeAnnotation\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n    typeAnnotation: validateType(\"TSType\"),\n  },\n};\n\ndefineType(\"TSAsExpression\", TSTypeExpression);\ndefineType(\"TSSatisfiesExpression\", TSTypeExpression);\n\ndefineType(\"TSTypeAssertion\", {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"typeAnnotation\", \"expression\"],\n  fields: {\n    typeAnnotation: validateType(\"TSType\"),\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSEnumBody\", {\n  visitor: [\"members\"],\n  fields: {\n    members: validateArrayOfType(\"TSEnumMember\"),\n  },\n});\n\nif (process.env.BABEL_8_BREAKING) {\n  defineType(\"TSEnumDeclaration\", {\n    // \"Statement\" alias prevents a semicolon from appearing after it in an export declaration.\n    aliases: [\"Statement\", \"Declaration\"],\n    visitor: [\"id\", \"body\"],\n    fields: {\n      declare: validateOptional(bool),\n      const: validateOptional(bool),\n      id: validateType(\"Identifier\"),\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      body: validateType(\"TSEnumBody\"),\n    },\n  });\n} else {\n  defineType(\"TSEnumDeclaration\", {\n    // \"Statement\" alias prevents a semicolon from appearing after it in an export declaration.\n    aliases: [\"Statement\", \"Declaration\"],\n    visitor: [\"id\", \"members\"],\n    fields: {\n      declare: validateOptional(bool),\n      const: validateOptional(bool),\n      id: validateType(\"Identifier\"),\n      members: validateArrayOfType(\"TSEnumMember\"),\n      initializer: validateOptionalType(\"Expression\"),\n      body: validateOptionalType(\"TSEnumBody\"),\n    },\n  });\n}\n\ndefineType(\"TSEnumMember\", {\n  visitor: [\"id\", \"initializer\"],\n  fields: {\n    id: validateType(\"Identifier\", \"StringLiteral\"),\n    initializer: validateOptionalType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSModuleDeclaration\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"body\"],\n  fields: {\n    kind: {\n      validate: assertOneOf(\"global\", \"module\", \"namespace\"),\n    },\n    declare: validateOptional(bool),\n    ...(!process.env.BABEL_8_BREAKING && { global: validateOptional(bool) }),\n    id: process.env.BABEL_8_BREAKING\n      ? validateType(\"TSEntityName\", \"StringLiteral\")\n      : validateType(\"Identifier\", \"StringLiteral\"),\n    body: process.env.BABEL_8_BREAKING\n      ? validateType(\"TSModuleBlock\")\n      : validateType(\"TSModuleBlock\", \"TSModuleDeclaration\"),\n  },\n});\n\ndefineType(\"TSModuleBlock\", {\n  aliases: [\"Scopable\", \"Block\", \"BlockParent\", \"FunctionParent\"],\n  visitor: [\"body\"],\n  fields: {\n    body: validateArrayOfType(\"Statement\"),\n  },\n});\n\ndefineType(\"TSImportType\", {\n  aliases: [\"TSType\"],\n  builder: [\n    \"argument\",\n    \"qualifier\",\n    process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\",\n  ],\n  visitor: [\n    \"argument\",\n    \"options\",\n    \"qualifier\",\n    process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\",\n  ],\n  fields: {\n    argument: process.env.BABEL_8_BREAKING\n      ? validateType(\"TSLiteralType\")\n      : validateType(\"StringLiteral\"),\n    qualifier: validateOptionalType(\"TSEntityName\"),\n    [process.env.BABEL_8_BREAKING ? \"typeArguments\" : \"typeParameters\"]:\n      validateOptionalType(\"TSTypeParameterInstantiation\"),\n    options: {\n      validate: assertNodeType(\"ObjectExpression\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TSImportEqualsDeclaration\", {\n  aliases: [\"Statement\", \"Declaration\"],\n  visitor: [\"id\", \"moduleReference\"],\n  fields: {\n    ...(process.env.BABEL_8_BREAKING ? {} : { isExport: validate(bool) }),\n    id: validateType(\"Identifier\"),\n    moduleReference: validateType(\"TSEntityName\", \"TSExternalModuleReference\"),\n    importKind: {\n      validate: assertOneOf(\"type\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TSExternalModuleReference\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"StringLiteral\"),\n  },\n});\n\ndefineType(\"TSNonNullExpression\", {\n  aliases: [\"Expression\", \"LVal\", \"PatternLike\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSExportAssignment\", {\n  aliases: [\"Statement\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: validateType(\"Expression\"),\n  },\n});\n\ndefineType(\"TSNamespaceExportDeclaration\", {\n  aliases: [\"Statement\"],\n  visitor: [\"id\"],\n  fields: {\n    id: validateType(\"Identifier\"),\n  },\n});\n\ndefineType(\"TSTypeAnnotation\", {\n  visitor: [\"typeAnnotation\"],\n  fields: {\n    typeAnnotation: {\n      validate: assertNodeType(\"TSType\"),\n    },\n  },\n});\n\ndefineType(\"TSTypeParameterInstantiation\", {\n  visitor: [\"params\"],\n  fields: {\n    params: validateArrayOfType(\"TSType\"),\n  },\n});\n\ndefineType(\"TSTypeParameterDeclaration\", {\n  visitor: [\"params\"],\n  fields: {\n    params: validateArrayOfType(\"TSTypeParameter\"),\n  },\n});\n\ndefineType(\"TSTypeParameter\", {\n  builder: [\"constraint\", \"default\", \"name\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"name\", \"constraint\", \"default\"]\n    : [\"constraint\", \"default\"],\n  fields: {\n    name: {\n      validate: !process.env.BABEL_8_BREAKING\n        ? assertValueType(\"string\")\n        : assertNodeType(\"Identifier\"),\n    },\n    in: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    out: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    const: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    constraint: {\n      validate: assertNodeType(\"TSType\"),\n      optional: true,\n    },\n    default: {\n      validate: assertNodeType(\"TSType\"),\n      optional: true,\n    },\n  },\n});\n"], "mappings": ";;AACA,IAAAA,MAAA,GAAAC,OAAA;AAeA,IAAAC,KAAA,GAAAD,OAAA;AAIA,IAAAE,GAAA,GAAAF,OAAA;AAEA,MAAMG,UAAU,GAAG,IAAAC,wBAAiB,EAAC,YAAY,CAAC;AAElD,MAAMC,IAAI,GAAG,IAAAC,sBAAe,EAAC,SAAS,CAAC;AAEvC,MAAMC,8BAA8B,GAAGA,CAAA,MAAO;EAC5CC,UAAU,EAAE;IACVC,QAAQ,EAGJ,IAAAC,qBAAc,EAAC,kBAAkB,EAAE,MAAM,CAAC;IAC9CC,QAAQ,EAAE;EACZ,CAAC;EACDC,cAAc,EAAE;IACdH,QAAQ,EAGJ,IAAAC,qBAAc,EAAC,4BAA4B,EAAE,MAAM,CAAC;IACxDC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEFR,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAsC,CAAC,MAAM,CAAC;EACrDC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,aAAa,EAAE;MACbP,QAAQ,EAAE,IAAAQ,kBAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;MACvDN,QAAQ,EAAE;IACZ,CAAC;IACDO,QAAQ,EAAE;MACRT,QAAQ,EAAE,IAAAH,sBAAe,EAAC,SAAS,CAAC;MACpCK,QAAQ,EAAE;IACZ,CAAC;IACDQ,SAAS,EAAE;MACTV,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY,EAAE,mBAAmB;IAC5D,CAAC;IACDU,QAAQ,EAAE;MACRX,QAAQ,EAAE,IAAAH,sBAAe,EAAC,SAAS,CAAC;MACpCK,QAAQ,EAAE;IACZ,CAAC;IACDU,UAAU,EAAE;MACVZ,QAAQ,EAAE,IAAAa,kBAAW,EAAC,WAAW,CAAC;MAClCX,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFR,UAAU,CAAC,mBAAmB,EAAE;EAC9BU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,CAAC;EACzDC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACD,IAAAC,+BAAyB,EAAC,CAAC,EAC3BlB,8BAA8B,CAAC,CAAC;AAEvC,CAAC,CAAC;AAEFJ,UAAU,CAAC,iBAAiB,EAAE;EAC5BW,OAAO,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,CAAC;EACxEC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACD,IAAAE,sCAAgC,EAAC,CAAC,EAClCnB,8BAA8B,CAAC,CAAC;AAEvC,CAAC,CAAC;AAEFJ,UAAU,CAAC,iBAAiB,EAAE;EAC5BU,OAAO,EAAE,CAAC,cAAc,CAAC;EACzBC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,MAAM,EAAE;IACNY,IAAI,EAAE,IAAAC,mBAAY,EAAC,cAAc,CAAC;IAClCC,KAAK,EAAE,IAAAD,mBAAY,EAAC,YAAY;EAClC;AACF,CAAC,CAAC;AAEF,MAAME,0BAA0B,GAAGA,CAAA,MAAO;EACxClB,cAAc,EAAE,IAAAmB,2BAAoB,EAAC,4BAA4B,CAAC;EAClE,CAA2C,YAAY,GAAG,IAAAC,0BAAmB,EAC3E,cAAc,EACd,YAAY,EACZ,eAAe,EACf,aACF,CAAC;EACD,CAA+C,gBAAgB,GAC7D,IAAAD,2BAAoB,EAAC,kBAAkB;AAC3C,CAAC,CAAC;AAEF,MAAME,iCAAiC,GAAG;EACxCpB,OAAO,EAAE,CAAC,eAAe,CAAC;EAC1BC,OAAO,EAAE,CACP,gBAAgB,EAC0B,YAAY,EACR,gBAAgB,CAC/D;EACDC,MAAM,EAAEe,0BAA0B,CAAC;AACrC,CAAC;AAED3B,UAAU,CAAC,4BAA4B,EAAE8B,iCAAiC,CAAC;AAC3E9B,UAAU,CACR,iCAAiC,EACjC8B,iCACF,CAAC;AAED,MAAMC,sBAAsB,GAAGA,CAAA,MAAO;EACpCC,GAAG,EAAE,IAAAP,mBAAY,EAAC,YAAY,CAAC;EAC/BQ,QAAQ,EAAE;IAAEC,OAAO,EAAE;EAAM,CAAC;EAC5B1B,QAAQ,EAAE,IAAA2B,uBAAgB,EAACjC,IAAI;AACjC,CAAC,CAAC;AAEFF,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAE,CAAC,eAAe,CAAC;EAC1BC,OAAO,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAClCC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACDU,sBAAsB,CAAC,CAAC;IAC3BhB,QAAQ,EAAE,IAAAoB,uBAAgB,EAACjC,IAAI,CAAC;IAChCkC,cAAc,EAAE,IAAAR,2BAAoB,EAAC,kBAAkB,CAAC;IACxDS,IAAI,EAAE;MACJ7B,QAAQ,EAAE,IAAI;MACdF,QAAQ,EAAE,IAAAQ,kBAAW,EAAC,KAAK,EAAE,KAAK;IACpC;EAAC;AAEL,CAAC,CAAC;AAEFd,UAAU,CAAC,mBAAmB,EAAE;EAC9BU,OAAO,EAAE,CAAC,eAAe,CAAC;EAC1BC,OAAO,EAAE,CACP,KAAK,EACL,gBAAgB,EAC0B,YAAY,EACR,gBAAgB,CAC/D;EACDC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACDM,0BAA0B,CAAC,CAAC,EAC5BI,sBAAsB,CAAC,CAAC;IAC3BM,IAAI,EAAE;MACJ/B,QAAQ,EAAE,IAAAQ,kBAAW,EAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;IAC9C;EAAC;AAEL,CAAC,CAAC;AAEFd,UAAU,CAAC,kBAAkB,EAAE;EAC7BU,OAAO,EAAE,CAAC,eAAe,CAAC;EAC1BC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCC,MAAM,EAAE;IACNG,QAAQ,EAAE,IAAAoB,uBAAgB,EAACjC,IAAI,CAAC;IAChCoC,MAAM,EAAE,IAAAH,uBAAgB,EAACjC,IAAI,CAAC;IAC9BqC,UAAU,EAAE,IAAAV,0BAAmB,EAAC,YAAY,CAAC;IAC7CO,cAAc,EAAE,IAAAR,2BAAoB,EAAC,kBAAkB;EACzD;AACF,CAAC,CAAC;AAEF,MAAMY,cAAc,GAAG,CACrB,cAAc,EACd,kBAAkB,EAClB,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,eAAe,CACP;AAEV,KAAK,MAAMC,IAAI,IAAID,cAAc,EAAE;EACjCxC,UAAU,CAACyC,IAAI,EAAE;IACf/B,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;IACjCC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,CAAC;EACX,CAAC,CAAC;AACJ;AAEAZ,UAAU,CAAC,YAAY,EAAE;EACvBU,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EACjCC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AAEF,MAAM8B,WAAW,GAAG;EAClBhC,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CACP,gBAAgB,EAC0B,YAAY,EACR,gBAAgB;AAElE,CAAC;AAEDX,UAAU,CAAC,gBAAgB,EAAAoB,MAAA,CAAAC,MAAA,KACtBqB,WAAW;EACd9B,MAAM,EAAEe,0BAA0B,CAAC;AAAC,EACrC,CAAC;AACF3B,UAAU,CAAC,mBAAmB,EAAAoB,MAAA,CAAAC,MAAA,KACzBqB,WAAW;EACd9B,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACDM,0BAA0B,CAAC,CAAC;IAC/BgB,QAAQ,EAAE,IAAAR,uBAAgB,EAACjC,IAAI;EAAC;AACjC,EACF,CAAC;AAEFF,UAAU,CAAC,iBAAiB,EAAE;EAC5BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CACP,UAAU,EACuC,gBAAgB,CAClE;EACDC,MAAM,EAAE;IACNgC,QAAQ,EAAE,IAAAnB,mBAAY,EAAC,cAAc,CAAC;IACtC,CAAkD,gBAAgB,GAChE,IAAAG,2BAAoB,EAAC,8BAA8B;EACvD;AACF,CAAC,CAAC;AAEF5B,UAAU,CAAC,iBAAiB,EAAE;EAC5BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;EAC5CkC,OAAO,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,SAAS,CAAC;EACvDjC,MAAM,EAAE;IACNkC,aAAa,EAAE,IAAArB,mBAAY,EAAC,YAAY,EAAE,YAAY,CAAC;IACvDW,cAAc,EAAE,IAAAR,2BAAoB,EAAC,kBAAkB,CAAC;IACxDmB,OAAO,EAAE,IAAAZ,uBAAgB,EAACjC,IAAI;EAChC;AACF,CAAC,CAAC;AAEFF,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CACP,UAAU,EACuC,gBAAgB,CAClE;EACDC,MAAM,EAAE;IACNoC,QAAQ,EAAE,IAAAvB,mBAAY,EAAC,cAAc,EAAE,cAAc,CAAC;IACtD,CAAkD,gBAAgB,GAChE,IAAAG,2BAAoB,EAAC,8BAA8B;EACvD;AACF,CAAC,CAAC;AAEF5B,UAAU,CAAC,eAAe,EAAE;EAC1BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBC,MAAM,EAAE;IACNqC,OAAO,EAAE,IAAApB,0BAAmB,EAAC,eAAe;EAC9C;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,aAAa,CAAC;EACxBC,MAAM,EAAE;IACNsC,WAAW,EAAE,IAAAzB,mBAAY,EAAC,QAAQ;EACpC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,cAAc,CAAC;EACzBC,MAAM,EAAE;IACNuC,YAAY,EAAE,IAAAtB,0BAAmB,EAAC,QAAQ,EAAE,oBAAoB;EAClE;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,gBAAgB,EAAE;EAC3BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,gBAAgB,CAAC;EAC3BC,MAAM,EAAE;IACNwB,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,YAAY,EAAE;EACvBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,gBAAgB,CAAC;EAC3BC,MAAM,EAAE;IACNwB,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,oBAAoB,EAAE;EAC/BW,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;EACjCkC,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC;EAC7CjC,MAAM,EAAE;IACNwC,KAAK,EAAE,IAAA3B,mBAAY,EAAC,YAAY,CAAC;IACjCjB,QAAQ,EAAE;MACRF,QAAQ,EAAEJ,IAAI;MACdgC,OAAO,EAAE;IACX,CAAC;IACDgB,WAAW,EAAE,IAAAzB,mBAAY,EAAC,QAAQ;EACpC;AACF,CAAC,CAAC;AAEF,MAAM4B,mBAAmB,GAAG;EAC1B3C,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,MAAM,EAAE;IACN0C,KAAK,EAAE,IAAAzB,0BAAmB,EAAC,QAAQ;EACrC;AACF,CAAC;AAED7B,UAAU,CAAC,aAAa,EAAEqD,mBAAmB,CAAC;AAC9CrD,UAAU,CAAC,oBAAoB,EAAEqD,mBAAmB,CAAC;AAErDrD,UAAU,CAAC,mBAAmB,EAAE;EAC9BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,CAAC;EAC9DC,MAAM,EAAE;IACN2C,SAAS,EAAE,IAAA9B,mBAAY,EAAC,QAAQ,CAAC;IACjC+B,WAAW,EAAE,IAAA/B,mBAAY,EAAC,QAAQ,CAAC;IACnCgC,QAAQ,EAAE,IAAAhC,mBAAY,EAAC,QAAQ,CAAC;IAChCiC,SAAS,EAAE,IAAAjC,mBAAY,EAAC,QAAQ;EAClC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,eAAe,CAAC;EAC1BC,MAAM,EAAE;IACN+C,aAAa,EAAE,IAAAlC,mBAAY,EAAC,iBAAiB;EAC/C;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,gBAAgB,CAAC;EAC3BC,MAAM,EAAE;IACNwB,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,gBAAgB,EAAE;EAC3BU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,gBAAgB,CAAC;EAC3BkC,OAAO,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC;EACvCjC,MAAM,EAAE;IACNgD,QAAQ,EAAE;MACRtD,QAAQ,EAEJ,IAAAH,sBAAe,EAAC,QAAQ;IAC9B,CAAC;IACDiC,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;EACpCC,MAAM,EAAE;IACNiD,UAAU,EAAE,IAAApC,mBAAY,EAAC,QAAQ,CAAC;IAClCqC,SAAS,EAAE,IAAArC,mBAAY,EAAC,QAAQ;EAClC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,cAAc,EAAE;EACzBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,OAAO,EAEH,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,CAAC;EACnDkC,OAAO,EAEH,CAAC,eAAe,EAAE,gBAAgB,EAAE,UAAU,CAAC;EACnDjC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KAMA;IACEsC,aAAa,EAAE,IAAAlC,mBAAY,EAAC,iBAAiB;EAC/C,CAAC;IACLV,QAAQ,EAAE,IAAAoB,uBAAgB,EAAC,IAAArB,kBAAW,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9DN,QAAQ,EAAE,IAAA2B,uBAAgB,EAAC,IAAArB,kBAAW,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9DsB,cAAc,EAAE,IAAAR,2BAAoB,EAAC,QAAQ,CAAC;IAC9CmC,QAAQ,EAAE,IAAAnC,2BAAoB,EAAC,QAAQ;EAAC;AAE5C,CAAC,CAAC;AAEF5B,UAAU,CAAC,uBAAuB,EAAE;EAClCU,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EACjCC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;EAC5BC,MAAM,EAAE;IACNoD,MAAM,EAAE,IAAAnC,0BAAmB,EAAC,iBAAiB,CAAC;IAC9CyB,KAAK,EAAE;MACLhD,QAAQ,EAAE,IAAA2D,YAAK,EACb,IAAA9D,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAA+D,iBAAU,EAAC,IAAA3D,qBAAc,EAAC,QAAQ,CAAC,CAAC,EACpC,UAAU4D,IAA6B,EAAEnC,GAAG,EAAEoC,GAAG,EAAE;QACjD,IAAID,IAAI,CAACH,MAAM,CAACK,MAAM,KAAKD,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;UACzC,MAAM,IAAIC,SAAS,CACjB,aACEH,IAAI,CAAC1B,IAAI,0EAET2B,GAAG,CAACC,MAAM,GAAG,CAAC,mBACGF,IAAI,CAACH,MAAM,CAACK,MAAM,EACvC,CAAC;QACH;MACF,CACF;IACF;EACF;AACF,CAAC,CAAC;AAEFrE,UAAU,CAAC,eAAe,EAAE;EAC1BU,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EACjCC,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBC,MAAM,EAAE;IACN2D,OAAO,EAAE;MACPjE,QAAQ,EAAG,YAAY;QACrB,MAAMkE,eAAe,GAAG,IAAAjE,qBAAc,EACpC,gBAAgB,EAChB,eACF,CAAC;QACD,MAAMkE,aAAa,GAAG,IAAA3D,kBAAW,EAAC,GAAG,CAAC;QAEtC,MAAMyD,OAAO,GAAG,IAAAhE,qBAAc,EAC5B,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,iBACF,CAAC;QACD,SAASmE,SAASA,CAACC,MAAW,EAAE3C,GAAW,EAAEmC,IAAS,EAAE;UAEtD,IAAI,IAAAS,WAAE,EAAC,iBAAiB,EAAET,IAAI,CAAC,EAAE;YAE/BM,aAAa,CAACN,IAAI,EAAE,UAAU,EAAEA,IAAI,CAACP,QAAQ,CAAC;YAC9CY,eAAe,CAACL,IAAI,EAAE,UAAU,EAAEA,IAAI,CAACU,QAAQ,CAAC;UAClD,CAAC,MAAM;YAELN,OAAO,CAACI,MAAM,EAAE3C,GAAG,EAAEmC,IAAI,CAAC;UAC5B;QACF;QAEAO,SAAS,CAACI,cAAc,GAAG,CACzB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,iBAAiB,CAClB;QAED,OAAOJ,SAAS;MAClB,CAAC,CAAE;IACL;EACF;AACF,CAAC,CAAC;AAmBK;EACL1E,UAAU,CAAC,+BAA+B,EAAE;IAC1CU,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnBC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;IACzCC,MAAM,EAAE;MACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,cAAc,CAAC;MACxChB,cAAc,EAAE,IAAAmB,2BAAoB,EAAC,8BAA8B;IACrE;EACF,CAAC,CAAC;AACJ;AAEA5B,UAAU,CAAC,wBAAwB,EAAE;EAEnCU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC;EACpDC,MAAM,EAAE;IACNoE,OAAO,EAAE,IAAA7C,uBAAgB,EAACjC,IAAI,CAAC;IAC/B+E,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY,CAAC;IAC9BhB,cAAc,EAAE,IAAAmB,2BAAoB,EAAC,4BAA4B,CAAC;IAClEsD,OAAO,EAAE,IAAA/C,uBAAgB,EACvB,IAAAhB,kBAAW,EAIL,+BACN,CACF,CAAC;IACDgE,IAAI,EAAE,IAAA1D,mBAAY,EAAC,iBAAiB;EACtC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,iBAAiB,EAAE;EAC5BW,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBC,MAAM,EAAE;IACNuE,IAAI,EAAE,IAAAtD,0BAAmB,EAAC,eAAe;EAC3C;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,wBAAwB,EAAE;EACnCU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;EACnDC,MAAM,EAAE;IACNoE,OAAO,EAAE,IAAA7C,uBAAgB,EAACjC,IAAI,CAAC;IAC/B+E,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY,CAAC;IAC9BhB,cAAc,EAAE,IAAAmB,2BAAoB,EAAC,4BAA4B,CAAC;IAClEQ,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,2BAA2B,EAAE;EACtCU,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAEH,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACpCC,MAAM,EAAE;IACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,YAAY,CAAC;IACtC,CAAkD,gBAAgB,GAChE,IAAAG,2BAAoB,EAAC,8BAA8B;EACvD;AACF,CAAC,CAAC;AAEF,MAAMwD,gBAAgB,GAAG;EACvB1E,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,aAAa,CAAC;EAC9CC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCC,MAAM,EAAE;IACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,YAAY,CAAC;IACtCW,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ;EACvC;AACF,CAAC;AAEDzB,UAAU,CAAC,gBAAgB,EAAEoF,gBAAgB,CAAC;AAC9CpF,UAAU,CAAC,uBAAuB,EAAEoF,gBAAgB,CAAC;AAErDpF,UAAU,CAAC,iBAAiB,EAAE;EAC5BU,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,aAAa,CAAC;EAC9CC,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC;EACzCC,MAAM,EAAE;IACNwB,cAAc,EAAE,IAAAX,mBAAY,EAAC,QAAQ,CAAC;IACtCsD,UAAU,EAAE,IAAAtD,mBAAY,EAAC,YAAY;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,YAAY,EAAE;EACvBW,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBC,MAAM,EAAE;IACNqC,OAAO,EAAE,IAAApB,0BAAmB,EAAC,cAAc;EAC7C;AACF,CAAC,CAAC;AAeK;EACL7B,UAAU,CAAC,mBAAmB,EAAE;IAE9BU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;IACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;IAC1BC,MAAM,EAAE;MACNoE,OAAO,EAAE,IAAA7C,uBAAgB,EAACjC,IAAI,CAAC;MAC/BmF,KAAK,EAAE,IAAAlD,uBAAgB,EAACjC,IAAI,CAAC;MAC7B+E,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY,CAAC;MAC9BwB,OAAO,EAAE,IAAApB,0BAAmB,EAAC,cAAc,CAAC;MAC5CyD,WAAW,EAAE,IAAA1D,2BAAoB,EAAC,YAAY,CAAC;MAC/CuD,IAAI,EAAE,IAAAvD,2BAAoB,EAAC,YAAY;IACzC;EACF,CAAC,CAAC;AACJ;AAEA5B,UAAU,CAAC,cAAc,EAAE;EACzBW,OAAO,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;EAC9BC,MAAM,EAAE;IACNqE,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY,EAAE,eAAe,CAAC;IAC/C6D,WAAW,EAAE,IAAA1D,2BAAoB,EAAC,YAAY;EAChD;AACF,CAAC,CAAC;AAEF5B,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EACvBC,MAAM,EAAAQ,MAAA,CAAAC,MAAA;IACJgB,IAAI,EAAE;MACJ/B,QAAQ,EAAE,IAAAQ,kBAAW,EAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW;IACvD,CAAC;IACDkE,OAAO,EAAE,IAAA7C,uBAAgB,EAACjC,IAAI;EAAC,GACM;IAAEqF,MAAM,EAAE,IAAApD,uBAAgB,EAACjC,IAAI;EAAE,CAAC;IACvE+E,EAAE,EAEE,IAAAxD,mBAAY,EAAC,YAAY,EAAE,eAAe,CAAC;IAC/C0D,IAAI,EAEA,IAAA1D,mBAAY,EAAC,eAAe,EAAE,qBAAqB;EAAC;AAE5D,CAAC,CAAC;AAEFzB,UAAU,CAAC,eAAe,EAAE;EAC1BU,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,gBAAgB,CAAC;EAC/DC,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBC,MAAM,EAAE;IACNuE,IAAI,EAAE,IAAAtD,0BAAmB,EAAC,WAAW;EACvC;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,cAAc,EAAE;EACzBU,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBmC,OAAO,EAAE,CACP,UAAU,EACV,WAAW,EACsC,gBAAgB,CAClE;EACDlC,OAAO,EAAE,CACP,UAAU,EACV,SAAS,EACT,WAAW,EACsC,gBAAgB,CAClE;EACDC,MAAM,EAAE;IACNiE,QAAQ,EAEJ,IAAApD,mBAAY,EAAC,eAAe,CAAC;IACjC+D,SAAS,EAAE,IAAA5D,2BAAoB,EAAC,cAAc,CAAC;IAC/C,CAAkD,gBAAgB,GAChE,IAAAA,2BAAoB,EAAC,8BAA8B,CAAC;IACtD6D,OAAO,EAAE;MACPnF,QAAQ,EAAE,IAAAC,qBAAc,EAAC,kBAAkB,CAAC;MAC5CC,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFR,UAAU,CAAC,2BAA2B,EAAE;EACtCU,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAClCC,MAAM,EAAAQ,MAAA,CAAAC,MAAA,KACoC;IAAEqE,QAAQ,EAAE,IAAApF,eAAQ,EAACJ,IAAI;EAAE,CAAC;IACpE+E,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY,CAAC;IAC9BkE,eAAe,EAAE,IAAAlE,mBAAY,EAAC,cAAc,EAAE,2BAA2B,CAAC;IAC1EmE,UAAU,EAAE;MACVtF,QAAQ,EAAE,IAAAQ,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;MACtCN,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFR,UAAU,CAAC,2BAA2B,EAAE;EACtCW,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,eAAe;EAC1C;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,qBAAqB,EAAE;EAChCU,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,aAAa,CAAC;EAC9CC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,YAAY;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,oBAAoB,EAAE;EAC/BU,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNmE,UAAU,EAAE,IAAAtD,mBAAY,EAAC,YAAY;EACvC;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,8BAA8B,EAAE;EACzCU,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,OAAO,EAAE,CAAC,IAAI,CAAC;EACfC,MAAM,EAAE;IACNqE,EAAE,EAAE,IAAAxD,mBAAY,EAAC,YAAY;EAC/B;AACF,CAAC,CAAC;AAEFzB,UAAU,CAAC,kBAAkB,EAAE;EAC7BW,OAAO,EAAE,CAAC,gBAAgB,CAAC;EAC3BC,MAAM,EAAE;IACNwB,cAAc,EAAE;MACd9B,QAAQ,EAAE,IAAAC,qBAAc,EAAC,QAAQ;IACnC;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,8BAA8B,EAAE;EACzCW,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,MAAM,EAAE;IACNiF,MAAM,EAAE,IAAAhE,0BAAmB,EAAC,QAAQ;EACtC;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,4BAA4B,EAAE;EACvCW,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBC,MAAM,EAAE;IACNiF,MAAM,EAAE,IAAAhE,0BAAmB,EAAC,iBAAiB;EAC/C;AACF,CAAC,CAAC;AAEF7B,UAAU,CAAC,iBAAiB,EAAE;EAC5B6C,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC;EAC1ClC,OAAO,EAEH,CAAC,YAAY,EAAE,SAAS,CAAC;EAC7BC,MAAM,EAAE;IACNkF,IAAI,EAAE;MACJxF,QAAQ,EACJ,IAAAH,sBAAe,EAAC,QAAQ;IAE9B,CAAC;IACD4F,EAAE,EAAE;MACFzF,QAAQ,EAAE,IAAAH,sBAAe,EAAC,SAAS,CAAC;MACpCK,QAAQ,EAAE;IACZ,CAAC;IACDwF,GAAG,EAAE;MACH1F,QAAQ,EAAE,IAAAH,sBAAe,EAAC,SAAS,CAAC;MACpCK,QAAQ,EAAE;IACZ,CAAC;IACD6E,KAAK,EAAE;MACL/E,QAAQ,EAAE,IAAAH,sBAAe,EAAC,SAAS,CAAC;MACpCK,QAAQ,EAAE;IACZ,CAAC;IACDyF,UAAU,EAAE;MACV3F,QAAQ,EAAE,IAAAC,qBAAc,EAAC,QAAQ,CAAC;MAClCC,QAAQ,EAAE;IACZ,CAAC;IACD0B,OAAO,EAAE;MACP5B,QAAQ,EAAE,IAAAC,qBAAc,EAAC,QAAQ,CAAC;MAClCC,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC", "ignoreList": []}